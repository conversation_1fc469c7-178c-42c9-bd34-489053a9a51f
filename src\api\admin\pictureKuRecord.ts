import request from '/@/utils/yhrequest';
const apiPrefix = `/api/xzgateway/yh-xz-salary/`;
// const apiPrefix = `/yh-xz-salary/`;

// 添加图片记录(通用方法,包括浏览和下载)
export const addImageGalleryRecord = (params: any, config = {}) =>
    request.post(apiPrefix + `addImageGalleryRecord`, params, config);

// 获取图片记录列表
export const getImageGalleryRecordList = (params: any, config = {}) =>
    request.post(apiPrefix + `getImageGalleryRecordList`, params, config);

// 统计图片记录(按图片ID分组)
export const getImageGalleryRecordStats = (params: any, config = {}) =>
    request.post(apiPrefix + `getImageGalleryRecordStats`, params, config);

// 统计图片记录(按日期分组)
export const getImageGalleryRecordDateStats = (params: any, config = {}) =>
    request.post(apiPrefix + `getImageGalleryRecordDateStats`, params, config);

// 导出图片记录
export const exportImageGalleryRecord = (params: any, config = { responseType: 'blob' }) =>
    request.post(apiPrefix + `exportImageGalleryRecord`, params, config);
