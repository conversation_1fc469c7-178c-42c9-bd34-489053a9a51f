<template>
	<Container>
		<template #content>
			<div class="container1">
				<div style="width: 100%">
					<el-input v-model="input" style="width: 100%" placeholder="公式" disabled :rows="3" type="textarea" resize="none" />
					<div>{{ field }}</div>
				</div>
				<div class="grid-btns">
					<div v-for="btn in allBtn" :key="btn" class="grid-btn" type="primary" size="large" @click="changeInput(btn)">
						<el-dropdown v-if="btn === '汇总类型'" trigger="hover" class="grid-btn_description" @command="summaryTypecommand" @click.stop>
							<div>
								汇总类型<el-icon><ArrowDown /></el-icon>
							</div>
							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item v-for="item in summaryTypeList" :key="item.value" :command="item.label">{{ item.label }}</el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
						<el-dropdown v-else-if="btn === '字段'" trigger="hover" class="grid-btn_description" @command="fieldcommand" @click.stop>
							<div>
								字段<el-icon><ArrowDown /></el-icon>
							</div>
							<template #dropdown>
								<el-dropdown-menu style="height: 200px">
									<el-dropdown-item v-for="item in allTableCols" :key="item.field" :command="item.title">{{ item.title }}</el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
						<div v-else class="grid-btn_description">{{ btn }}</div>
					</div>
				</div>
			</div>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineModel, defineEmits, inject, watch } from 'vue';
const input = ref(''); //显示的公式
const allBtn = ['汇总类型', '字段', '清空', '回退', '关闭', 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, '.', ' + ', ' - ', ' * ', ' / ', ' ( ', ' ) ', ' > ', ' < ', ' = '];
const summaryTypeList = ref([
	{ label: '总数( ', value: 'Sum( ', conditionValue: 'SumIf( ' }, //SumIf(
	{ label: '条数( ', value: 'Count( ', conditionValue: 'CountIf( ' }, //CountIf(
	{ label: '平均数( ', value: 'Avg( ', conditionValue: 'AvgIf( ' }, //AvgIf(
	{ label: '最大值( ', value: 'Max( ', conditionValue: 'MaxIf( ' }, //MaxIf(
	{ label: '最小值( ', value: 'Min( ', conditionValue: 'MinIf( ' }, //MinIf(
	{ label: '去重条数( ', value: 'Count(Distinct ', conditionValue: 'CountIf(Distinct ' }, //CountIf(Distinct
]);
const allTableCols = inject<VxeTable.Columns[] | RefType>('allTableCols');
const emit = defineEmits(['close']);
const valueList = ref<Array<string | number>>([]);
const field = defineModel<string>('field');
const value = defineModel<string>('value');
const changeInput = (e: string | number) => {
	if (e == '汇总类型' || e == '字段') return;
	if (e == '清空') {
		input.value = '';
		valueList.value = [];
		return;
	} else if (e == '回退') {
		if (valueList.value.length > 0) {
			const res = valueList.value.pop();
			console.log(res, 'res');
			input.value = input.value.slice(0, -String(res).length);
			//如果有汇总类型
			const summary = summaryTypeList.value.find((item) => ' ' + item.label + ' ' === res);
			if (summary) {
				field.value = field.value?.slice(0, -summary.value.length);
			} else {
				field.value = field.value?.slice(0, -String(` ${res} `).length);
			}
		}
		return;
	} else if (e == '关闭') {
		emit('close');
		return;
	}
	valueList.value.push(e);
	field.value += String(e); // 入参
	input.value += e;
};

const summaryTypecommand = (e: string) => {
	input.value += e;
	valueList.value.push(e);
	field.value += summaryTypeList.value.find((item) => item.label === e)?.value || '';
};

const fieldcommand = (e: string) => {
	input.value += ' ' + e + ' ';
	valueList.value.push(e);
	field.value += allTableCols?.value?.find((item: VxeTable.Columns) => item.title === e)?.field || '';
};
</script>

<style scoped lang="scss">
.container1 {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background-color: #ccc;

	.grid-btns {
		display: grid;
		grid-template-columns: repeat(5, 75px);
		grid-gap: 16px;
		margin: 32px 0;
		justify-content: center;
		justify-items: end;

		.grid-btn {
			border-radius: 10px;
			width: 100%;
			height: 50px;
			font-size: 20px;
			padding: 0;
			cursor: pointer;
			background-color: #409eff;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #000;
			background-color: #fcfcfd;
			font-size: 16px;
			:hover {
				background-color: #dfdfdf;
				border-radius: 10px;
			}

			.grid-btn_description {
				width: 100%;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
}
</style>
