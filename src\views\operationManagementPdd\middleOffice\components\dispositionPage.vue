<template>
	<div>
		<el-form ref="ruleFormRef" style="max-width: 600px" :model="ruleForm" status-icon :rules="rules" label-width="auto" class="demo-ruleForm">
			<el-form-item label="负责人">
				<el-select class="publicCss" v-model="ruleForm.directorId" placeholder="负责人" clearable filterable style="width: 200px">
					<el-option label="无负责人" value="0"> </el-option>
					<el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="组长">
				<el-select class="publicCss" v-model="ruleForm.groupId" placeholder="组长" clearable filterable style="width: 200px">
					<el-option label="无组长" value="0"> </el-option>
					<el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
			</el-form-item>
			<el-form-item :label="`${configType == 1 ? '比例(%)' : '比例'}`" prop="proportion">
				<el-input-number
					v-model="ruleForm.proportion"
					:placeholder="`${configType == 1 ? '请输入比例(%)' : '请输入比例'}`"
					:min="0"
					:max="9999"
					:controls="false"
					:precision="2"
					style="width: 200px"
				/>
			</el-form-item>
			<el-form-item>
				<el-button @click="emit('close')"> 取消 </el-button>
				<el-button type="primary" @click="submitForm(ruleFormRef)">保存</el-button>
			</el-form-item>
		</el-form>
	</div>
</template>

<script setup lang="ts" name="">
import { defineProps, onMounted, ref, defineEmits } from 'vue';
import { getDirectorList } from '/@/api/operatemanage/shop';
import { GetConfigMessage, AddEditConfigMessage } from '/@/api/operatemanage/operate';
import type { FormInstance, FormRules } from 'element-plus';
const props = defineProps({
	configType: {
		type: Number,
		default: 1, //
	},
});
const ruleFormRef = ref<FormInstance>();
const emit = defineEmits(['close', 'getList']);
const ruleForm = ref({
	directorId: null,
	groupId: null,
	proportion: null,
});
const rules = ref({
	proportion: [{ required: true, message: '请输入比例', trigger: 'blur' }],
});
const directorlist = ref<Public.options[]>([]);
const getOperateUserName = async () => {
	const { data } = await getDirectorList();
	directorlist.value = data?.map((item: any) => {
		return {
			label: item.value,
			value: item.key,
		};
	});
};

const getProps = async () => {
	const { data, success } = await GetConfigMessage({ configType: props.configType });
	if (!success) return;
	if (data && data?.length > 0) {
		data[0].groupId = String(data[0].groupId);
		data[0].directorId = String(data[0].directorId);
	}
	ruleForm.value =
		data && data?.length > 0
			? data[0]
			: {
					directorId: null,
					groupId: null,
					proportion: null,
				};
	console.log(ruleForm.value);
};

const submitForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate(async (valid, fields) => {
		if (valid) {
			const { success } = await AddEditConfigMessage({
				...ruleForm.value,
				configType: props.configType,
			});
			if (!success) return;
			window.$message.success('提交成功');
			emit('close');
			emit('getList');
		} else {
			console.log('error submit!', fields);
		}
	});
};

onMounted(async () => {
	await getOperateUserName();
	await getProps();
});
</script>

<style scoped lang="scss">
.demo-ruleForm {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}
</style>
