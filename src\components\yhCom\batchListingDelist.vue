<template>
	<Container>
		<template #header>
			<div class="top">
				<span class="spanCss">商品ID:</span>
				<div class="publicCss" style="width: 250px">
					<manyInput v-model:inputt="query.proCodes" title="商品ID" :verifyNumber="false" placeholder="请输入商品ID(若输入多条请按回车)" :maxRows="1000" :maxlength="21000" />
				</div>
				<el-button type="primary" @click="getList()" style="margin-right: 20px">搜索</el-button>
				<el-button type="primary" @click="batchRack(1)">批量上架</el-button>
				<el-button type="primary" @click="batchRack(3)">批量下架</el-button>

				<el-button type="primary" @click="onExport">导出</el-button>
				<el-button type="primary" @click="bulkLoadingUnLog">查看日志</el-button>
				<el-button type="primary" v-auth="'api:operatemanage:productmanager:DeletePDDProduct'" @click="deletePDDProductMethod">删除链接</el-button>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				id="202506121509"
				:tableCols="tableCols"
				showsummary
				isIndexFixed
				:query="query"
				:query-api="GetProductUpDownList"
				isNeedDisposeProps
				@disposeProps="disposeProps"
				:isNeedPager="false"
			>
			</vxetable>
			<el-dialog v-model="dialogVisible" title="批量上下架日志" width="1000px">
				<batchListingDelistLog :proCodes="query.proCodes" v-if="dialogVisible" style="height: 650px" />
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted } from 'vue';
const manyInput = defineAsyncComponent(() => import('/@/components/yhCom/manyInput.vue'));
import { GetProductUpDownList, ExportProductUpDownList, BatchProductUpDown, DeletePDDProduct } from '/@/api/operatemanage/productManager';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const batchListingDelistLog = defineAsyncComponent(() => import('/@/components/yhCom/batchListingDelistLog.vue'));
import { ElMessage, ElMessageBox } from 'element-plus';
const props = defineProps({
	checkdata: {
		type: Array,
		default: () => [],
	},
});
const query = ref({
	proCodes: '',
});
const dialogVisible = ref(false);
const table = ref();
const loading = ref(false);
const tableData = ref([]);

const bulkLoadingUnLog = () => {
	dialogVisible.value = true;
};

const deletePDDProductMethod = async () => {
	let selprocodes: string[] = [];
	tableData.value.forEach((f: any) => {
		selprocodes.push(f.proCode);
	});
	ElMessageBox.confirm('确定删除吗?', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		let res = await DeletePDDProduct(selprocodes);
		if (res?.success) {
			ElMessage.success('删除成功');
			table.value.refreshTable(true);
		}
	});
};

const batchRack = async (val: number) => {
	let action = val == 1 ? '上架' : '下架';
	var check = false;
	tableData.value.forEach((row: any) => {
		if (row.isDel) check = true;
	});
	if (check) {
		ElMessage.error('存在已删除的产品,不可继续操作');
		return;
	}
	let content = `此操作将批量"${action}", 是否继续?`;
	ElMessageBox.confirm(content, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			loading.value = true;
			await BatchProductUpDown({ proList: tableData.value, proStatus: val }).then((data: any) => {
				loading.value = false;
				if (data.success) {
					ElMessage.success('操作成功，请关注钉钉消息接收上下架成功数量!');
					table.value.refreshTable(true);
				}
			});
		})
		.catch(() => {
			ElMessage.info('已取消操作');
		});
};

const onExport = async () => {
	loading.value = true;
	await ExportProductUpDownList({ ...query.value, ...table.value.query })
		.then((data: any) => {
			loading.value = false;
			const aLink = document.createElement('a');
			let blob = new Blob([data], { type: 'application/vnd.ms-excel' });
			aLink.href = URL.createObjectURL(blob);
			aLink.setAttribute('download', '违规数据统计-批量上下架列表_' + new Date().toLocaleString() + '.xlsx');
			aLink.click();
		})
		.catch(() => {
			loading.value = false;
		});
};

const getList = () => {
	table.value.refreshTable(true);
};

const onUpDownStatus = (val: number) => {
	return val == 1 ? '上架中' : val == 2 ? '下架中' : val == 3 ? '已上架' : val == 4 ? '已下架' : val == 5 ? '上架失败' : val == 6 ? '下架失败' : val == 7 ? '已删除' : val == 8 ? '删除失败' : '';
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'proCode', title: '商品ID', align: 'center', width: '110' },
	{ sortable: true, field: 'proName', title: '产品名称', align: 'center', width: '160' },
	{ sortable: true, field: 'shopName', title: '店铺', align: 'center', width: '110' },
	{
		sortable: true,
		field: 'proStatus',
		title: '当前状态',
		align: 'center',
		width: '80',
		formatter: (row: any) => (row.proStatus == 0 ? '未知' : row.proStatus == 1 ? '上架' : row.proStatus == 3 ? '下架' : ''),
	},
	{ sortable: true, field: 'upTime', title: '上架时间', align: 'center', width: '120' },
	{ sortable: true, field: 'downTime', title: '下架时间', align: 'center', width: '120' },
	{ sortable: true, field: 'isDel', title: '是否删除', align: 'center', width: '80', formatter: (row: any) => (row.isDel ? '是' : '否') },
	{ sortable: true, field: 'delTime', title: '删除时间', align: 'center', width: '120' },
	{
		sortable: true,
		field: 'upDownStatus',
		title: '操作状态',
		align: 'center',
		width: '80',
		formatter: (row: any) => onUpDownStatus(row.upDownStatus),
	},
	{ sortable: true, field: 'opTime', title: '操作时间', align: 'center', width: '120' },
	{ sortable: true, field: 'opEndTime', title: '操作完成时间', align: 'center', width: '120' },
	{ sortable: true, field: 'opUserName', title: '操作人', align: 'center', width: '80' },
	{ sortable: true, field: 'opResult', title: '操作结果', align: 'center', width: '100' },
]);

const disposeProps = async (data: any, callback: Function) => {
	tableData.value = data.allData || [];
	callback(data);
};

onMounted(() => {
	query.value.proCodes = props.checkdata.map((f: any) => f.proCode).join(',') || '';
});
</script>

<style scoped lang="scss">
.top {
	display: flex;
	width: 100%;
	margin-bottom: 3px;
	align-items: center;

	.spanCss {
		display: flex;
		align-items: center;
		margin-right: 5px;
	}

	.publicCss {
		width: 250px;
		margin-right: 10px;
	}
}
</style>
