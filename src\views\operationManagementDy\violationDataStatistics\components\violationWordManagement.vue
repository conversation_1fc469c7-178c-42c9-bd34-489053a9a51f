<template>
	<Container>
		<template #header>
			<div class="topCss">
				<el-input v-model.trim="query.productId" placeholder="产品ID" clearable maxlength="50" class="publicCss_top" />
				<el-input v-model.trim="query.productName" placeholder="产品名称" clearable maxlength="50" class="publicCss_top" />
				<el-select v-model="query.platform" placeholder="平台" class="publicCss_top" clearable filterable @change="shopInit">
					<el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.shopcodes" placeholder="店铺名称" class="public_Css" clearable filterable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false">
					<el-option v-for="item in shopOptions" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input v-model.trim="query.shopCode" placeholder="店铺ID" class="publicCss_top" clearable maxlength="50" />
				<el-select v-model="query.groupIds" placeholder="运营组" class="public_Css" clearable filterable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false">
					<el-option v-for="item in props.groupList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<el-select v-model="query.operateSpecialId" placeholder="运营专员" class="public_Css" filterable clearable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false">
					<el-option v-for="item in props.directorlist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.user1Id" placeholder="运营助理" class="public_Css" filterable clearable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false">
					<el-option v-for="item in props.directorlist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input v-model.trim="query.sensitiveWord" placeholder="违规词" clearable maxlength="50" class="publicCss_top" />
				<el-input v-model.trim="query.styleCode" placeholder="系列编码" clearable maxlength="50" class="publicCss_top" />
				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="202506121058" :tableCols="tableCols" showsummary isIndexFixed :query="query" :query-api="GetProSensitiveWord">
				<template #toolbar_buttons>
					<el-button type="primary" @click="exportProps">导出</el-button>
					<el-button type="primary" @click="configDialogVisible = true" v-auth="'violationWordConfigurePermissions'">配置</el-button>
					<el-button type="primary" @click="onPull">拉取</el-button>
					<!-- <el-button type="primary" @click="onWhitelistProps">白名单</el-button> -->
				</template>
			</vxetable>
			<el-dialog v-model="configDialogVisible" title="配置" width="40%" draggable overflow :close-on-click-modal="false" :destroy-on-close="true" v-loading="configTableLoading">
				<el-form :model="configForm" ref="configFormRef" label-width="100px">
					<div style="display: flex; align-items: center">
						<el-form-item label="违规词" prop="sensitiveWord" style="margin-bottom: 15px">
							<div style="display: flex; align-items: center">
								<el-input v-model.trim="configForm.sensitiveWord" style="width: 250px" placeholder="请输入违规词" clearable maxlength="50" />
								<el-button type="primary" style="margin-left: 10px" @click="onQuery">查询</el-button>
								<el-button type="primary" style="margin-left: 10px" @click="addSensitiveWords">添加</el-button>
								<el-button type="primary" style="margin-left: 10px" @click="exportConfig" :disabled="isExport">导出</el-button>
							</div>
						</el-form-item>
					</div>
				</el-form>
				<vxetable ref="configTable" id="202506131150" :tableCols="configTableCols" showsummary isIndexFixed :query="configForm" :query-api="SensitiveWordsQuery" style="height: 400px"></vxetable>
			</el-dialog>

			<el-dialog v-model="whitelistVisible" title="白名单" width="40%" draggable overflow :close-on-click-modal="false" :destroy-on-close="true" v-loading="whitelistTableLoading">
				<el-form :model="whitelistForm" ref="whitelistFormRef" label-width="100px">
					<div style="display: flex; align-items: center; flex-wrap: nowrap; gap: 10px; margin-bottom: 10px">
						<el-form-item label="违规词" prop="sensitiveWord" style="margin-bottom: 0; flex-shrink: 0">
							<el-select v-model="whitelistForm.sensitiveWord" placeholder="请选择违规词" style="width: 150px" clearable filterable>
								<el-option v-for="item in prohibitedOptions" :key="item" :label="item" :value="item" />
							</el-select>
						</el-form-item>
						<el-form-item label="系列编码" prop="styleCode" style="margin-bottom: 0; flex-shrink: 0">
							<el-select
								style="width: 180px"
								v-model="whitelistForm.styleCode"
								filterable
								clearable
								remote
								remote-show-suffix
								:remote-method="remoteMethod"
								:reserve-keyword="false"
								class="publicCss"
								placeholder="请模糊输入并选择系列编码"
								v-loading="remoteLoading"
								suffix-icon=""
							>
								<el-option v-for="item in codingList" :key="item" :label="item" :value="item"> </el-option>
							</el-select>
						</el-form-item>
						<el-button type="primary" @click="onWhitelistQuery">查询</el-button>
						<el-button type="primary" @click="addWhitelist">添加</el-button>
					</div>
				</el-form>
				<vxetable
					ref="whitelistTable"
					id="202507181120"
					:tableCols="whitelistTableCols"
					showsummary
					isIndexFixed
					:query="whitelistForm"
					orderBy="createdTime"
					is-Asc="false"
					:query-api="whitelistQueryApi"
					style="height: 400px"
				></vxetable>
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, PropType, onMounted } from 'vue';
import {
	SensitiveWordsQuery,
	SensitiveWordsAdd,
	SensitiveWordsDelete,
	ProdContainsSensitiveWords,
	GetProSensitiveWord,
	ExportProdContainsSensitiveWords,
	SensitiveWordsWhiteListAdd,
	SensitiveWordsWhiteListQuery,
	SensitiveWordsWhiteListDelete,
	ExportSensitiveWords,
	WhiteListQueryStyleCode,
} from '/@/api/operatemanage/productManager';
import dayjs from 'dayjs';
import { ElMessage, ElMessageBox } from 'element-plus';
import { platformlist } from '/@/utils/tools';
import { debounce } from 'lodash-es';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
interface OptionItem {
	value: string | number;
	label: string;
}
const props = defineProps({
	groupList: {
		type: Array as PropType<OptionItem[]>,
		default: () => [],
	},
	directorlist: {
		type: Array as PropType<OptionItem[]>,
		default: () => [],
	},
	shopList: {
		type: Array as PropType<OptionItem[]>,
		default: () => [],
	},
});

const remoteLoading = ref(false);
const codingList = ref<string[]>([]);
const isExport = ref(false);

// 包装白名单查询API，添加错误处理
const whitelistQueryApi = async (params: any) => {
	try {
		const result = await SensitiveWordsWhiteListQuery(params);
		// 确保返回的数据结构符合vxeTable的预期
		if (result && typeof result === 'object') {
			return {
				data: result.data || { list: [], total: 0 },
				success: result.success !== false,
			};
		}
		// 如果API返回的数据不符合预期，返回默认结构
		return {
			data: { list: [], total: 0 },
			success: false,
		};
	} catch (error) {
		console.error('白名单查询API调用失败:', error);
		// 返回默认的数据结构，避免解构错误
		return {
			data: { list: [], total: 0 },
			success: false,
		};
	}
};
const whitelistVisible = ref(false);
const whitelistForm = ref({
	sensitiveWord: '',
	styleCode: '',
});
const whitelistTable = ref();
const whitelistTableLoading = ref(false);
const prohibitedOptions = ref<OptionItem[]>([]);
const configDialogVisible = ref(false);
const shopOptions = ref<OptionItem[]>([]);

const query = ref({
	shopcodes: [],
	shopCode: '',
	groupIds: [],
	operateSpecialId: [],
	user1Id: [],
	productId: '',
	productName: '',
	platform: null as any,
	sensitiveWord: '',
	styleCode: '',
});
const configForm = ref({
	sensitiveWord: '',
});
const configFormRef = ref();
const table = ref();
const configTable = ref();
const configTableLoading = ref(false);
const handleDelete = (row: any) => {
	ElMessageBox.confirm('确定删除该违规词吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		configTableLoading.value = true;
		const { success } = await SensitiveWordsDelete({ id: row.id, createdUserId: row.createdUserId });
		configTableLoading.value = false;
		if (success) {
			ElMessage.success('删除成功');
			configTable.value.refreshTable(true);
		}
	});
};

const remoteMethod = debounce(async (query: any) => {
	remoteLoading.value = true;
	const params = {
		styleCode: query,
		pageSize: 10,
		currentPage: 1,
	};
	if (query !== '') {
		codingList.value = [];
		const data = await WhiteListQueryStyleCode(params);
		if (data) {
			const list = (data as unknown as { data: string[] }).data || [];
			codingList.value = [...new Set(list.filter((item: string) => !!item))];
		}
	}
	remoteLoading.value = false;
}, 800);

const onWhitelistQuery = () => {
	whitelistTable.value.refreshTable(true);
};

const onWhitelistProps = () => {
	whitelistForm.value.styleCode = '';
	whitelistForm.value.sensitiveWord = '';
	whitelistVisible.value = true;
};

const addWhitelist = async () => {
	if (!whitelistForm.value.sensitiveWord) {
		ElMessage.error('请输入需添加的违规词');
		return;
	}
	if (!whitelistForm.value.styleCode) {
		ElMessage.error('请输入需添加的系列编码');
		return;
	}
	whitelistTableLoading.value = true;
	const { success } = await SensitiveWordsWhiteListAdd(whitelistForm.value);
	whitelistTableLoading.value = false;
	if (success) {
		whitelistForm.value.sensitiveWord = '';
		whitelistForm.value.styleCode = '';
		whitelistTable.value.refreshTable(true);
		ElMessage.success('添加成功');
	}
};

const handleWhitelistDelete = (row: any) => {
	ElMessageBox.confirm('确定删除该白名单吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		whitelistTableLoading.value = true;
		const { success } = await SensitiveWordsWhiteListDelete({ id: row.id, createdUserId: row.createdUserId });
		whitelistTableLoading.value = false;
		if (success) {
			ElMessage.success('删除成功');
			whitelistTable.value.refreshTable(true);
		}
	});
};

const exportProps = async () => {
	await ExportProdContainsSensitiveWords({ ...query.value, ...table.value.query })
		.then((data: any) => {
			ElMessage[data.success ? 'success' : 'error'](data.msg || (data.success ? '导出成功' : '导出失败'));
		})
		.catch(() => {});
};

const shopInit = async (e: any) => {
	if (e) {
		shopOptions.value = props.shopList.filter((item: any) => item.platform === e);
	} else {
		shopOptions.value = props.shopList;
	}
};

const addSensitiveWords = async () => {
	if (!configForm.value.sensitiveWord) {
		ElMessage.error('请输入需添加的违规词');
		return;
	}
	configTableLoading.value = true;
	const { success } = await SensitiveWordsAdd(configForm.value);
	configTableLoading.value = false;
	if (success) {
		ElMessage.success('添加成功');
		configForm.value.sensitiveWord = '';
		configTable.value.refreshTable(true);
	}
};

const exportConfig = async () => {
	isExport.value = true;
	await ExportSensitiveWords({ ...configForm.value, ...configTable.value.query })
		.then((data: any) => {
			if (data) {
				const aLink = document.createElement('a');
				let blob = new Blob([data], { type: 'application/vnd.ms-excel' });
				aLink.href = URL.createObjectURL(blob);
				aLink.setAttribute('download', '违规词配置导出数据' + new Date().toLocaleString() + '.xlsx');
				aLink.click();
				isExport.value = false;
			}
		})
		.catch(() => {
			isExport.value = false;
		});
};

const onPull = () => {
	ElMessageBox.confirm('确定拉取吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		const { success } = await ProdContainsSensitiveWords(query.value);
		if (success) {
			ElMessage.success('拉取成功');
			table.value.refreshTable(true);
		}
	});
};

const onQuery = () => {
	configTable.value.refreshTable(true);
};

const whitelistTableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'sensitiveWord', align: 'center', title: '违规词' },
	{ sortable: true, field: 'styleCode', align: 'center', title: '系列编码' },
	{ sortable: true, field: 'createdUserName', align: 'center', title: '添加人' },
	{ sortable: true, field: 'createdTime', align: 'center', title: '添加时间' },
	{
		title: '操作',
		align: 'center',
		width: '70',
		type: 'btnList',
		minWidth: '70',
		field: 'operation',
		btnList: [{ title: '删除', handle: handleWhitelistDelete }],
		fixed: 'right',
	},
]);
const configTableCols = ref<VxeTable.Columns[]>([
	{ field: 'sensitiveWord', align: 'center', title: '违规词' },
	{ field: 'createdUserName', align: 'center', title: '添加人' },
	{ sortable: true, field: 'createdTime', align: 'center', title: '添加时间' },
	{
		title: '操作',
		align: 'center',
		width: '70',
		type: 'btnList',
		minWidth: '70',
		field: 'operation',
		btnList: [{ title: '删除', handle: handleDelete }],
		fixed: 'right',
	},
]);
const tableCols = ref<VxeTable.Columns[]>([
	{ field: 'proCode', align: 'center', title: '产品ID', formatter: (row: any) => row.proCode || '' },
	{ field: 'title', align: 'center', title: '产品名称', width: '400', formatter: (row: any) => row.title || '' },
	{ field: 'platform', title: '平台', align: 'center', formatter: 'formatPlatform' },
	{ field: 'shopName', align: 'center', title: '店铺' },
	{ field: 'shopCode', align: 'center', title: '店铺ID' },
	{ field: 'groupId', align: 'center', title: '运营组', formatter: (row: any) => row.groupName || '' },
	{ field: 'operateSpecialUserId', align: 'center', title: '运营专员', formatter: (row: any) => row.operateSpecialName || '' },
	{ field: 'userId', align: 'center', title: '运营助理', formatter: (row: any) => row.user1Name || '' },
	{ field: 'sensitiveWord', align: 'center', title: '违规词' },
	{ field: 'styleCode', align: 'center', title: '系列编码' },
]);

onMounted(async () => {
	shopOptions.value = props.shopList;
	const { data, success } = await SensitiveWordsQuery({ currentPage: 1, pageSize: 99999999 });
	if (success) {
		prohibitedOptions.value = data?.list.map((item: any) => item.sensitiveWord);
	}
});
</script>

<style scoped lang="scss">
.public_Css {
	width: 180px;
	margin: 0 5px 5px 0;
}
.publicCss_top {
	width: 140px;
	margin: 0 5px 5px 0;
}
::v-deep .el-select__tags-text {
	max-width: 65px;
}
</style>
