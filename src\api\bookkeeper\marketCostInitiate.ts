import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_BookKeeper}/MarketCostInitiate/`;

//直通车流程自动化-查询
export const GetMarketCostInitiateList = (params: any, config = {}) => {
	return request.post(apiPrefix + 'GetMarketCostInitiateList', params, config);
};

//直通车流程自动化-导出
export const ExportMarketCostInitiateReviewList = (params: any, config = {}) => {
	return request.post(apiPrefix + 'ExportMarketCostInitiateReviewList', params, config);
};

//直通车流程自动化-获取发起营销费用审批详情
export const GetMarketCostInitiateReviewApplyData = (params: any, config = {}) => {
	return request.post(apiPrefix + 'GetMarketCostInitiateReviewApplyData', params, config);
};

//直通车流程自动化-编辑
export const MarketCostInitiateModify = (params: any, config = {}) => {
	return request.post(apiPrefix + 'MarketCostInitiateModify', params, config);
};

//直通车流程自动化-发起营销费用流程审批
export const SendCwMarketCostInitiateApply = (params: any, config = {}) => {
	return request.post(apiPrefix + 'SendCwMarketCostInitiateApply', params, config);
};
