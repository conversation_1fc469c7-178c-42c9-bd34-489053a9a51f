<template>
	<Container v-loading="loading">
		<template #header>
			<div class="topCss">
				<el-select v-model="query.sensitiveWord" placeholder="请选择违规词" style="width: 150px; margin: 0 5px 5px 0" clearable filterable>
					<el-option v-for="item in prohibitedOptions" :key="item" :label="item" :value="item" />
				</el-select>
				<el-select
					v-model="query.styleCode"
					style="width: 180px; margin: 0 5px 5px 0"
					filterable
					clearable
					remote
					remote-show-suffix
					:remote-method="remoteMethod"
					:reserve-keyword="false"
					class="publicCss"
					placeholder="请模糊输入并选择系列编码"
					v-loading="remoteLoading"
				>
					<el-option v-for="item in codingList" :key="item" :label="item" :value="item" />
				</el-select>
				<div class="pb5">
					<el-button type="primary" @click="getList">查询</el-button>
					<el-button type="primary" @click="addWhitelist">添加</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="configTable" id="whitelist202507291331" :tableCols="tableCols" showsummary isIndexFixed :query="query" orderBy="createdTime" is-Asc="false" :query-api="whitelistQueryApi">
				<template #information="{ row }">
					<div v-if="row.information">
						<template v-for="(url, index) in row.information.split(',')" :key="index">
							<div style="margin-bottom: 5px; display: inline-block; margin-right: 10px">
								<!-- PDF文件显示 -->
								<el-image
									v-if="url.indexOf('.pdf') > -1"
									style="width: 60px; height: 60px; cursor: pointer"
									:src="'https://nanc.yunhanmy.com:10010/media/video/20241103/1853027836488163329.png'"
									@click="openPdfPreview(url)"
									fit="cover"
								>
									<template #error>
										<div style="display: flex; justify-content: center; align-items: center; width: 100%; height: 100%; background: #f5f7fa; color: #909399">PDF</div>
									</template>
								</el-image>
								<!-- 图片文件显示 -->
								<el-image
									v-else-if="isImageFile(url)"
									style="width: 60px; height: 60px"
									:src="url"
									:preview-src-list="getImageUrls(row.information)"
									:initial-index="getImageUrls(row.information).indexOf(url)"
									:hide-on-click-modal="true"
									fit="cover"
								>
									<template #error>
										<div style="display: flex; justify-content: center; align-items: center; width: 100%; height: 100%; background: #f5f7fa; color: #909399">图片</div>
									</template>
								</el-image>
								<!-- 其他文件类型显示链接 -->
								<div v-else style="padding: 5px; border: 1px solid #ddd; border-radius: 4px; background: #f9f9f9">
									<a :href="url" target="_blank" style="color: #409eff; text-decoration: none">
										{{ getFileName(url) }}
									</a>
								</div>
							</div>
						</template>
					</div>
					<div v-else style="color: #909399">暂无文件</div>
				</template>
			</vxetable>
			<el-dialog v-model="editPriceVisible" title="编辑" width="20%" draggable overflow :close-on-click-modal="false">
				<div style="display: flex; width: 100%">
					<el-form :model="singForm" :rules="rules" ref="ruleFormRef" label-width="110px" width="150px">
						<el-form-item label="资料">
							<uploadMf
								ref="refUploadMf"
								v-if="editPriceVisible"
								v-model:imagesStr="singForm.informationArr"
								uploadName="上传图片或PDF文件"
								uploadFormat=".jpg,.png,.gif,.jpeg,.pdf"
								:upstyle="{ height: 40, width: 40 }"
								:limit="1"
							></uploadMf>
						</el-form-item>
					</el-form>
				</div>
				<div style="display: flex; justify-content: center; margin-top: 20px">
					<el-button @click="editPriceVisible = false">取消</el-button>
					<el-button type="primary" @click="onSortSave(ruleFormRef)" v-reclick="1000">保存</el-button>
				</div>
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
// Vue相关导入
import { ref, defineAsyncComponent, onMounted } from 'vue';
import { ElMessage, FormInstance, ElMessageBox } from 'element-plus';
import { debounce } from 'lodash-es';

// API导入
import {
	SensitiveWordsQuery,
	SensitiveWordsDelete,
	SensitiveWordsWhiteListAdd,
	SensitiveWordsWhiteListQuery,
	WhiteListQueryStyleCode,
	SensitiveWordsWhiteListEdit,
} from '/@/api/operatemanage/productManager';

// 组件导入
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const uploadMf = defineAsyncComponent(() => import('/@/components/yhCom/uploadMf.vue'));

// 响应式数据
const refUploadMf = ref();
const loading = ref(false);
const remoteLoading = ref(false);
const configTable = ref();
const prohibitedOptions = ref<string[]>([]);
const codingList = ref<string[]>([]);
const query = ref({
	sensitiveWord: '',
	styleCode: '',
});
const singForm = ref({
	informationArr: [],
	id: '',
});
const editPriceVisible = ref(false);
const rules = ref({
	informationArr: [{ required: true, message: '请上传资料', trigger: 'blur' }],
});
const ruleFormRef = ref();

// 查询方法
const getList = () => {
	configTable.value.refreshTable(true);
};

// 远程搜索方法
const remoteMethod = debounce(async (searchQuery: string) => {
	if (!searchQuery) return;

	remoteLoading.value = true;
	try {
		const params = {
			styleCode: searchQuery,
			pageSize: 10,
			currentPage: 1,
		};
		codingList.value = [];
		const data = await WhiteListQueryStyleCode(params);
		if (data) {
			const list = (data as unknown as { data: string[] }).data || [];
			codingList.value = [...new Set(list.filter((item: string) => !!item))];
		}
	} finally {
		remoteLoading.value = false;
	}
}, 800);

// 添加白名单
const addWhitelist = async () => {
	if (!query.value.sensitiveWord) {
		ElMessage.error('请输入需添加的违规词');
		return;
	}
	if (!query.value.styleCode) {
		ElMessage.error('请输入需添加的系列编码');
		return;
	}

	loading.value = true;
	try {
		const { success } = await SensitiveWordsWhiteListAdd(query.value);
		if (success) {
			query.value.sensitiveWord = '';
			query.value.styleCode = '';
			configTable.value.refreshTable(true);
			ElMessage.success('添加成功');
		}
	} finally {
		loading.value = false;
	}
};

// 白名单查询API
const whitelistQueryApi = async (params: any) => {
	try {
		const result = await SensitiveWordsWhiteListQuery(params);
		if (result && typeof result === 'object') {
			return {
				data: result.data || { list: [], total: 0 },
				success: result.success !== false,
			};
		}
		return {
			data: { list: [], total: 0 },
			success: false,
		};
	} catch (error) {
		console.error('白名单查询API调用失败:', error);
		return {
			data: { list: [], total: 0 },
			success: false,
		};
	}
};

//编辑处理
const handleEdit = (row: any) => {
	editPriceVisible.value = true;
	//清除校验
	if (ruleFormRef?.value) {
		ruleFormRef.value?.resetFields();
		ruleFormRef.value?.clearValidate();
	}
	singForm.value.id = row.id;
	singForm.value.informationArr = row.information ? row.information.split(',') : [];
};

const onSortSave = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate(async (valid, fields) => {
		if (valid) {
			// 获取上传组件中的文件信息
			const uploadedFiles = refUploadMf.value?.getUploadedFiles() || [];
			const latestFile = refUploadMf.value?.getLatestUploadedFile();
			console.log('所有上传的文件:', uploadedFiles);
			console.log('最新上传的文件:', latestFile);
			const fileUrls = uploadedFiles.map((file: any) => file.fileUrl);
			let { success } = await SensitiveWordsWhiteListEdit({
				...singForm.value,
				information: fileUrls.join(','),
			});
			if (success) {
				ElMessage.success('保存成功');
				editPriceVisible.value = false;
				getList();
			}
		} else {
			ElMessage.error('请填写完整信息');
		}
	});
};

// 删除处理
function handleDelete(row: any) {
	ElMessageBox.confirm('确定删除该违规词吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		loading.value = true;
		try {
			const { success } = await SensitiveWordsDelete({
				id: row.id,
				createdUserId: row.createdUserId,
			});
			if (success) {
				ElMessage.success('删除成功');
				configTable.value.refreshTable(true);
			}
		} finally {
			loading.value = false;
		}
	});
}

// 表格配置
const tableCols = ref<VxeTable.Columns[]>([
	{ field: 'sensitiveWord', align: 'center', title: '违规词' },
	{ field: 'styleCode', align: 'center', title: '系列编码' },
	{ field: 'information', align: 'center', title: '资料' },
	{ field: 'createdUserName', align: 'center', title: '添加人' },
	{ sortable: true, field: 'createdTime', align: 'center', title: '添加时间' },
	{
		title: '操作',
		align: 'center',
		width: '120',
		type: 'btnList',
		minWidth: '120',
		field: 'operation',
		btnList: [
			{ title: '编辑', handle: handleEdit },
			{ title: '删除', handle: handleDelete },
		],
		fixed: 'right',
	},
]);

// 辅助方法
// 判断是否为图片文件
const isImageFile = (url: string) => {
	const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif'];
	return imageExtensions.some((ext) => url.toLowerCase().includes(ext));
};

// 获取文件名
const getFileName = (url: string) => {
	return url.split('/').pop() || 'unknown';
};

// 获取所有图片URL（用于预览）
const getImageUrls = (information: string) => {
	if (!information) return [];
	return information.split(',').filter((url) => isImageFile(url));
};

// 打开PDF预览
const openPdfPreview = (url: string) => {
	window.open(url, '_blank');
};

// 初始化
onMounted(async () => {
	const { data, success } = await SensitiveWordsQuery({
		currentPage: 1,
		pageSize: 99999999,
	});
	if (success) {
		prohibitedOptions.value = data?.list.map((item: any) => item.sensitiveWord) || [];
	}
});
</script>

<style scoped lang="scss">
::v-deep .el-select__tags-text {
	max-width: 70px;
}
</style>
