<template>
	<Container v-loading="loading">
		<template #header>
			<div class="topCss">
				<el-select v-model="query.sensitiveWord" placeholder="请选择违规词" style="width: 150px" clearable filterable>
					<el-option v-for="item in prohibitedOptions" :key="item" :label="item" :value="item" />
				</el-select>
				<el-select
					style="width: 180px"
					v-model="query.styleCode"
					filterable
					clearable
					remote
					remote-show-suffix
					:remote-method="remoteMethod"
					:reserve-keyword="false"
					class="publicCss"
					placeholder="请模糊输入并选择系列编码"
					v-loading="remoteLoading"
					suffix-icon=""
				>
					<el-option v-for="item in codingList" :key="item" :label="item" :value="item"> </el-option>
				</el-select>
				<div class="pb5">
					<el-button type="primary" @click="getList">查询</el-button>
					<el-button type="primary" @click="addWhitelist">添加</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="configTable"
				id="2025071811201"
				:tableCols="tableCols"
				showsummary
				isIndexFixed
				:query="query"
				orderBy="createdTime"
				is-Asc="false"
				:query-api="whitelistQueryApi"
				style="height: 400px"
			></vxetable>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, PropType, onMounted } from 'vue';
import dayjs from 'dayjs';
import {
	SensitiveWordsQuery,
	SensitiveWordsAdd,
	SensitiveWordsDelete,
	ProdContainsSensitiveWords,
	ExportProdContainsSensitiveWords,
	SensitiveWordsWhiteListAdd,
	SensitiveWordsWhiteListQuery,
	SensitiveWordsWhiteListDelete,
	ExportSensitiveWords,
	WhiteListQueryStyleCode,
} from '/@/api/operatemanage/productManager';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
import { ElMessage, ElMessageBox } from 'element-plus';
import { debounce } from 'lodash-es';
const props = defineProps({
	shopList: {
		type: Array as PropType<any[]>,
		default: () => [],
	},
	directorlist: {
		type: Array as PropType<any[]>,
		default: () => [],
	},
});
interface OptionItem {
	value: string | number;
	label: string;
}
const configTableLoading = ref(false);
const prohibitedOptions = ref<OptionItem[]>([]);
const configTable = ref();
const table = ref();
const codingList = ref<string[]>([]);
const remoteLoading = ref(false);
const loading = ref(false);
const getList = async () => {
	configTable.value.refreshTable(true);
};
const query = ref({
	sensitiveWord: '',
	styleCode: '',
});

const remoteMethod = debounce(async (query: any) => {
	remoteLoading.value = true;
	const params = {
		styleCode: query,
		pageSize: 10,
		currentPage: 1,
	};
	if (query !== '') {
		codingList.value = [];
		const data = await WhiteListQueryStyleCode(params);
		if (data) {
			const list = (data as unknown as { data: string[] }).data || [];
			codingList.value = [...new Set(list.filter((item: string) => !!item))];
		}
	}
	remoteLoading.value = false;
}, 800);

const addWhitelist = async () => {
	if (!query.value.sensitiveWord) {
		ElMessage.error('请输入需添加的违规词');
		return;
	}
	if (!query.value.styleCode) {
		ElMessage.error('请输入需添加的系列编码');
		return;
	}
	loading.value = true;
	const { success } = await SensitiveWordsWhiteListAdd(query.value);
	loading.value = false;
	if (success) {
		query.value.sensitiveWord = '';
		query.value.styleCode = '';
		configTable.value.refreshTable(true);
		ElMessage.success('添加成功');
	}
};

const whitelistQueryApi = async (params: any) => {
	try {
		const result = await SensitiveWordsWhiteListQuery(params);
		// 确保返回的数据结构符合vxeTable的预期
		if (result && typeof result === 'object') {
			return {
				data: result.data || { list: [], total: 0 },
				success: result.success !== false,
			};
		}
		// 如果API返回的数据不符合预期，返回默认结构
		return {
			data: { list: [], total: 0 },
			success: false,
		};
	} catch (error) {
		console.error('白名单查询API调用失败:', error);
		// 返回默认的数据结构，避免解构错误
		return {
			data: { list: [], total: 0 },
			success: false,
		};
	}
};

const handleDelete = (row: any) => {
	ElMessageBox.confirm('确定删除该违规词吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		configTableLoading.value = true;
		const { success } = await SensitiveWordsDelete({ id: row.id, createdUserId: row.createdUserId });
		configTableLoading.value = false;
		if (success) {
			ElMessage.success('删除成功');
			configTable.value.refreshTable(true);
		}
	});
};
const tableCols = ref<VxeTable.Columns[]>([
	{ field: 'sensitiveWord', align: 'center', title: '违规词' },
	{ field: 'createdUserName', align: 'center', title: '添加人' },
	{ sortable: true, field: 'createdTime', align: 'center', title: '添加时间' },
	{
		title: '操作',
		align: 'center',
		width: '70',
		type: 'btnList',
		minWidth: '70',
		field: 'operation',
		btnList: [{ title: '删除', handle: handleDelete }],
		fixed: 'right',
	},
]);

onMounted(async () => {
	const { data, success } = await SensitiveWordsQuery({ currentPage: 1, pageSize: 99999999 });
	if (success) {
		prohibitedOptions.value = data?.list.map((item: any) => item.sensitiveWord);
	}
});
</script>

<style scoped lang="scss">
.public_Css {
	width: 185px;
	margin: 0 10px 5px 0;
}

::v-deep .el-select__tags-text {
	max-width: 70px;
}
</style>
