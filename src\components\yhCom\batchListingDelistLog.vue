<template>
	<Container>
		<template #header>
			<div class="top">
				<dataRange v-model:startDate="query.startDate" v-model:endDate="query.endDate" class="publicCss" style="width: 250px" :clearable="false" />
				<el-input v-model="query.logName" placeholder="操作人" style="width: 110px" maxlength="20" />
				<span class="spanCss">商品ID:</span>
				<div class="publicCss">
					<div class="publicCss" style="width: 250px">
						<manyInput v-model:inputt="query.proCodes" title="商品ID" :verifyNumber="false" placeholder="请输入商品ID(若输入多条请按回车)" :maxRows="1000" :maxlength="21000" />
					</div>
				</div>
				<el-button type="primary" @click="getList" style="margin-right: 20px">搜索</el-button>
				<el-button type="primary" @click="onExport()" style="margin-right: 20px">导出</el-button>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="202506121739" :tableCols="tableCols" showsummary isIndexFixed :query="query" :query-api="GetProductUpDownLogPageList"> </vxetable>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted } from 'vue';
const manyInput = defineAsyncComponent(() => import('/@/components/yhCom/manyInput.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
import { GetProductUpDownLogPageList, ExportProductUpDownLogList } from '/@/api/operatemanage/productManager';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
import { ElMessage, ElMessageBox } from 'element-plus';
import dayjs from 'dayjs';
const props = defineProps({
	proCodes: {
		type: String,
		default: '',
	},
});
const query = ref({
	proCodes: '',
	startDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
	endDate: dayjs().format('YYYY-MM-DD'),
	logName: '',
});
const table = ref();
const loading = ref(false);

const onExport = async () => {
	loading.value = true;
	await ExportProductUpDownLogList({ ...query.value, ...table.value.query })
		.then((data: any) => {
			loading.value = false;
			const aLink = document.createElement('a');
			let blob = new Blob([data], { type: 'application/vnd.ms-excel' });
			aLink.href = URL.createObjectURL(blob);
			aLink.setAttribute('download', '违规数据统计-批量上下架日志_' + new Date().toLocaleString() + '.xlsx');
			aLink.click();
		})
		.catch(() => {
			loading.value = false;
		});
};

const getList = () => {
	table.value.refreshTable(true);
};

const onUpDownStatus = (val: number) => {
	return val == 1 ? '上架中' : val == 2 ? '下架中' : val == 3 ? '已上架' : val == 4 ? '已下架' : val == 5 ? '上架失败' : val == 6 ? '下架失败' : val == 7 ? '已删除' : val == 8 ? '删除失败' : '';
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'proCode', title: '商品ID', align: 'center', width: '120' },
	{
		sortable: true,
		field: 'upDownStatus',
		title: '操作状态',
		align: 'center',
		width: '80',
		formatter: (row: any) => onUpDownStatus(row.upDownStatus),
	},
	{ sortable: true, field: 'opTime', title: '操作时间', align: 'center', width: '150' },
	{ sortable: true, field: 'opEndTime', title: '操作完成时间', align: 'center', width: '150' },
	{ sortable: true, field: 'opUserName', title: '操作人', align: 'center', width: '80' },
	{ sortable: true, field: 'opResult', title: '操作结果', align: 'center', width: '280' },
]);

onMounted(() => {
	query.value.proCodes = props.proCodes;
});
</script>

<style scoped lang="scss">
.top {
	display: flex;
	width: 100%;
	margin-bottom: 3px;
	align-items: center;

	.spanCss {
		display: flex;
		align-items: center;
		margin-right: 5px;
	}

	.publicCss {
		width: 250px;
		margin-right: 10px;
	}
}
</style>
