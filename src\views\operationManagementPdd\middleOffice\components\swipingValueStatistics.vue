<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" v-model:startDate="query.startTime" v-model:endDate="query.endTime" style="width: 200px" :clearable="false" />
				<el-input v-model.trim="query.assignPerson" placeholder="姓名" maxlength="50" clearable class="publicCss" />
				<el-input v-model.trim="query.director" placeholder="负责人" maxlength="50" clearable class="publicCss" />
				<el-input v-model.trim="query.groupName" placeholder="组长" maxlength="50" clearable class="publicCss" />
				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
					<el-button type="primary" @click="dispositionVisible = true">配置</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				id="20250313094053"
				:tableCols="tableCols"
				showsummary
				isIndexFixed
				:query="query"
				:query-api="GetStatisticsOfBrushingValuePageList"
				:export-api="ExportStatisticsOfBrushingValue"
				:asyncExport="{ title: '刷单价值统计', isAsync: false }"
			>
				<template #toolbar_buttons>
					<span style="color: red; margin-left: 10px">更新数据日期为昨天！</span>
				</template>
			</vxetable>
			<el-dialog v-model="dispositionVisible" title="配置" width="20%" draggable overflow :close-on-click-modal="false" append-to-body>
				<dispositionPage :configType="2" @getList="table.refreshTable(true)" @close="dispositionVisible = false" v-if="dispositionVisible" />
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent } from 'vue';
import dayjs from 'dayjs';
import { GetStatisticsOfBrushingValuePageList, ExportStatisticsOfBrushingValue } from '/@/api/operatemanage/operate';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
const dispositionPage = defineAsyncComponent(() => import('./dispositionPage.vue'));
const query = ref({
	//昨天
	startTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
	endTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
	assignPerson: '',
	director: '',
	groupName: '',
});
const dispositionVisible = ref(false);
const table = ref();
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'dateRange', title: '日期' },
	{ sortable: true, field: 'assignPerson', align: 'right', title: '姓名' },
	{ sortable: true, field: 'director', title: '负责人', align: 'right' },
	{ sortable: true, field: 'groupName', title: '组长', align: 'right' },
	{ sortable: true, field: 'brushOrderNumber', title: '刷单数量', align: 'right', formatter: 'fmtAmt0' },
	{ sortable: true, field: 'merit', title: '价值', align: 'right', formatter: 'fmtAmt2' },
]);
</script>

<style scoped lang="scss"></style>
