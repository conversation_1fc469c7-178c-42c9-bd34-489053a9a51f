<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" v-model:startDate="query.startDate" v-model:endDate="query.endDate" style="width: 200px" :clearable="false" />
				<shopSelect v-model:valueList="query.platformShopIDList" multiple field="platformShopID" class="publicCss" style="width: 290px" />
				<el-select class="publicCss" v-model="query.groupIdList" placeholder="运营组" clearable filterable multiple collapse-tags collapse-tags-tooltip>
					<el-option label="无运营组" :value="0"> </el-option>
					<el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<div class="numRangeCss">
					<div>店铺质量体验排名</div>
					<numRange v-model:maxNum="query.shopQltExpRankEnd" v-model:minNum="query.shopQltExpRankStart" :precision="2" class="publicCss" minPlaceHolder="最小值" maxPlaceHolder="最大值" />
				</div>
				<div class="numRangeCss">
					<div>店铺评价分排名</div>
					<numRange v-model:maxNum="query.shopRatingRankEnd" v-model:minNum="query.shopRatingRankStart" class="publicCss" minPlaceHolder="最小值" maxPlaceHolder="最大值" />
				</div>
				<div class="numRangeCss">
					<div>问题订单数</div>
					<numRange v-model:maxNum="query.errOrderCntEnd" v-model:minNum="query.errOrderCntStart" class="publicCss" minPlaceHolder="最小值" maxPlaceHolder="最大值" />
				</div>
				<div class="numRangeCss">
					<div>问题订单比</div>
					<numRange v-model:maxNum="query.errOrderRateEnd" v-model:minNum="query.errOrderRateStart" :precision="2" class="publicCss" minPlaceHolder="最小值" maxPlaceHolder="最大值" />
				</div>
				<div class="numRangeCss">
					<div>近30天差评数</div>
					<numRange v-model:maxNum="query.r30dNegRevCntEnd" v-model:minNum="query.r30dNegRevCntStart" class="publicCss" minPlaceHolder="最小值" maxPlaceHolder="最大值" />
				</div>
				<div class="numRangeCss">
					<div>近30天差评率(%)</div>
					<numRange v-model:maxNum="query.r30dNegRevRateEnd" v-model:minNum="query.r30dNegRevRateStart" :precision="2" class="publicCss" minPlaceHolder="最小值" maxPlaceHolder="最大值" />
				</div>
				<div class="numRangeCss">
					<div>近30天负向反馈数</div>
					<numRange v-model:maxNum="query.r30dNegFbkCntEnd" v-model:minNum="query.r30dNegFbkCntStart" class="publicCss" minPlaceHolder="最小值" maxPlaceHolder="最大值" />
				</div>
				<div class="numRangeCss">
					<div>近30天负向反馈率(%)</div>
					<numRange v-model:maxNum="query.r30dNegFbkRateEnd" v-model:minNum="query.r30dNegFbkRateStart" :precision="2" class="publicCss" minPlaceHolder="最小值" maxPlaceHolder="最大值" />
				</div>
				<div class="numRangeCss">
					<div>预警商品数</div>
					<numRange v-model:maxNum="query.eWarnGoodsCntEnd" v-model:minNum="query.eWarnGoodsCntStart" class="publicCss" minPlaceHolder="最小值" maxPlaceHolder="最大值" />
				</div>
				<div class="numRangeCss">
					<div>品质违规商品数</div>
					<numRange v-model:maxNum="query.qltVioGoodsCntEnd" v-model:minNum="query.qltVioGoodsCntStart" class="publicCss" minPlaceHolder="最小值" maxPlaceHolder="最大值" />
				</div>
				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				id="20250708105124"
				:tableCols="tableCols"
				showsummary
				isIndexFixed
				:query="query"
				:query-api="PageShopAspectListAsync"
				:export-api="ExportShopAspectListAsync"
				:asyncExport="{ title: '店铺维度管理', isAsync: false }"
			>
			</vxetable>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted } from 'vue';
import { PageShopAspectListAsync, ExportShopAspectListAsync } from '/@/api/operatemanage/operateTool';
import { getDirectorGroupList, GetShopList } from '/@/api/operatemanage/shop';
import dayjs from 'dayjs';
const shopSelect = defineAsyncComponent(() => import('/@/components/yhCom/shopSelect.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
import { platformlist } from '/@/utils/tools';
const groupList = ref<any[]>([]);
const shopList = ref<any[]>([]);
const query = ref({
	startDate: dayjs().format('YYYY-MM-DD'),
	endDate: dayjs().format('YYYY-MM-DD'),
	platformShopIDList: [],
	groupIdList: [],
	shopQltExpRankStart: null,
	shopQltExpRankEnd: null,
	shopRatingRankStart: null,
	shopRatingRankEnd: null,
	errOrderCntStart: null,
	errOrderCntEnd: null,
	r30dNegRevRateStart: null,
	r30dNegRevRateEnd: null,
	r30dNegFbkCntStart: null,
	r30dNegFbkCntEnd: null,
	r30dNegFbkRateStart: null,
	r30dNegFbkRateEnd: null,
	eWarnGoodsCntStart: null,
	eWarnGoodsCntEnd: null,
	qltVioGoodsCntStart: null,
	qltVioGoodsCntEnd: null,
	errOrderRateEnd: null,
	errOrderRateStart: null,
	r30dNegRevCntEnd: null,
	r30dNegRevCntStart: null,
});
const table = ref();
const filterShopList = (queryStr: string) => {
	return shopList.value.filter((item: any) => item.label.toLowerCase().includes(queryStr.toLowerCase()));
};
const tableCols = ref<VxeTable.Columns[]>([
	{ width: 90, sortable: true, field: 'gpDate', title: '日期', formatter: 'formatDate' },
	{ sortable: true, field: 'shopName', align: 'right', title: '店铺名称' },
	{ sortable: true, field: 'platformShopID', title: '店铺ID', align: 'right' },
	{ width: 75, sortable: true, field: 'groupId', title: '运营组', align: 'right', formatter: (row: any) => row.groupIdName },
	{ width: 65, sortable: true, field: 'startLevel', title: '星级', align: 'right' },
	{ width: 130, sortable: true, field: 'shopQltFeelRank', title: '店铺质量体验排名', align: 'right' },
	{ sortable: true, field: 'shopRatingRank', title: '店铺评价分排名', align: 'right' },
	{ width: 100, sortable: true, field: 'errOrderCnt', title: '问题订单数', align: 'right', formatter: 'fmtAmt0' },
	{ width: 110, sortable: true, field: 'errOrderRate', title: '问题订单占比', align: 'right', formatter: 'fmtPercent' },
	{ sortable: true, field: 'r30dNegRevCnt', title: '近30天差评数', align: 'right', formatter: 'fmtAmt0' },
	{ sortable: true, field: 'r30dNegRevRate', title: '近30天差评率', align: 'right', formatter: 'fmtPercent' },
	{ sortable: true, field: 'r30dNegFbkCnt', title: '近30天负向反馈数', align: 'right', formatter: 'fmtAmt0' },
	{ sortable: true, field: 'r30dNegFbkRate', title: '近30天负向反馈率', align: 'right', formatter: 'fmtPercent' },
	{ width: 100, sortable: true, field: 'eWarnGoodsCnt', title: '预警商品数', align: 'right', formatter: 'fmtAmt0' },
	{ sortable: true, field: 'qltVioGoodsCnt', title: '品质违规商品数', align: 'right' },
]);

onMounted(async () => {
	let { data, success } = await getDirectorGroupList();
	if (data && success) {
		groupList.value = groupList.value.concat(
			data.map((item: any) => {
				return { value: item.key, label: item.value };
			})
		);
	}
});
</script>

<style scoped lang="scss">
.numRangeCss {
	display: flex;
	align-items: center;
	gap: 10px;
	margin: 0 5px 5px 0;
	font-size: 12px;
	.publicCss {
		margin-bottom: 0px;
	}
}

:deep(.el-select__tags-text) {
	max-width: 40px;
}
</style>
