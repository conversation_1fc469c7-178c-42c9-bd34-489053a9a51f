<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" v-model:startDate="query.startDate" v-model:endDate="query.endDate" style="width: 200px" :clearable="false" />
				<el-select v-model="query.status" class="publicCss" placeholder="状态" clearable>
					<el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input v-model.trim="query.instanceId" placeholder="流程编号" clearable maxlength="50" class="publicCss" />
				<shopSelect v-model:label="query.shopName" field="shopName" class="publicCss" placeholder="店铺名称" clearable filterable />
				<el-input v-model.trim="query.shopCode" placeholder="店铺编码" clearable maxlength="50" class="publicCss" />
				<el-input v-model.trim="query.initiateUserName" placeholder="发起人" clearable maxlength="50" class="publicCss" />
				<div class="pb5">
					<el-button type="primary" @click="getList">查询</el-button>
					<el-button type="primary" @click="exportProps">导出</el-button>
					<el-button type="primary" @click="initiateMethod">发起营销费用</el-button>
				</div>
			</div>
		</template>
		<template #content v-loading="loading">
			<vxetable
				ref="table"
				id="processAutomationZtcIndex202507261637"
				:tableCols="tableCols"
				:query="query"
				:query-api="GetMarketCostInitiateList"
				showsummary
				isNeedCheckBox
				@select="onCheckBoxMethod"
			>
				<template #toolbar_buttons>
					<div style="color: red">注：列表拉取的日期为昨天</div>
				</template>
			</vxetable>

			<!-- ==================== 编辑对话框 ==================== -->
			<el-dialog v-model="editVisible" title="编辑" width="450" draggable overflow style="margin-top: -18vh !important" @close="handleClose" :close-on-click-modal="false">
				<div style="padding-top: 10px" v-loading="listLoading">
					<el-form :model="singleform" :rules="singlerules" ref="ruleFormRef">
						<el-form-item label="支付截图" :label-width="'100px'" prop="payPictureArr">
							<uploadMf v-if="editVisible" v-model:imagesStr="singleform.payPictureArr" :upstyle="{ height: 40, width: 40 }" :limit="1" ref="refuploadMf" />
						</el-form-item>
						<el-form-item label="店铺截图" :label-width="'100px'" prop="shopPictureArr">
							<uploadMf v-if="editVisible" v-model:imagesStr="singleform.shopPictureArr" :upstyle="{ height: 40, width: 40 }" :limit="1" ref="refuploadMfA" />
						</el-form-item>
					</el-form>
				</div>
				<template #footer>
					<div style="display: flex; justify-content: center; align-items: center; gap: 20px">
						<el-button @click="editVisible = false">取消</el-button>
						<el-button type="primary" @click="onSingleSave(ruleFormRef)" :disabled="listLoading">确定</el-button>
					</div>
				</template>
			</el-dialog>

			<!-- ==================== 发起营销费用对话框 ==================== -->
			<el-dialog v-model="initiateVisible" title="发起营销费用" width="450" draggable overflow style="margin-top: -12vh !important" @close="initiateVisible = false" :close-on-click-modal="false">
				<div style="padding-top: 10px" v-loading="initiateLoading">
					<el-form :model="initiateform" :rules="initiateRules" ref="initiateRef">
						<el-form-item label="收款账号" :label-width="'100px'" prop="accountNo">
							<el-input v-model.trim="initiateform.accountNo" placeholder="收款账号" class="btnGroup" clearable maxlength="50" />
						</el-form-item>
						<el-form-item label="收款户名" :label-width="'100px'" prop="accountName">
							<el-input v-model.trim="initiateform.accountName" placeholder="收款户名" class="btnGroup" clearable maxlength="50" />
						</el-form-item>
						<el-form-item label="开户行" :label-width="'100px'" prop="openAccountBank">
							<el-select v-model="initiateform.openAccountBank" placeholder="请选择开户行" class="btnGroup" clearable filterable>
								<el-option v-for="item in bankOptions" :key="item" :label="item" :value="item" />
							</el-select>
						</el-form-item>
						<el-form-item label="支付时间" :label-width="'100px'" prop="payTime">
							<el-date-picker v-model="initiateform.payTime" type="datetime" placeholder="选择日期" class="btnGroup" value-format="YYYY-MM-DD HH:mm:ss" :clearable="false" style="width: 100%" />
						</el-form-item>
					</el-form>
				</div>
				<template #footer>
					<div style="display: flex; justify-content: center; align-items: center; gap: 20px">
						<el-button @click="initiateVisible = false">取消</el-button>
						<el-button type="primary" @click="initiateProps" :disabled="initiateLoading">确定</el-button>
					</div>
				</template>
			</el-dialog>

			<!-- ==================== 详情对话框 ==================== -->
			<el-dialog v-model="detailVisible" title="详情" width="900" draggable overflow style="margin-top: -10vh !important" @close="detailVisible = false" :close-on-click-modal="false">
				<div style="padding-top: 10px">
					<el-descriptions direction="vertical" :column="6" :size="'default'" border label-align="right">
						<el-descriptions-item label="申请事由">{{ detailInfo.applyReason }}</el-descriptions-item>
						<el-descriptions-item label="报销/预支">{{ detailInfo.businessCategory }}</el-descriptions-item>
						<el-descriptions-item label="店铺编号">{{ detailInfo.shopCode }}</el-descriptions-item>
						<el-descriptions-item label="店铺">{{ detailInfo.shopName }}</el-descriptions-item>
						<el-descriptions-item label="费用分类">{{ detailInfo.costType }}</el-descriptions-item>
						<el-descriptions-item label="使用平台">{{ detailInfo.platform }}</el-descriptions-item>
						<el-descriptions-item label="金额（元）">{{ detailInfo.amount }}</el-descriptions-item>
						<el-descriptions-item label="大写">{{ numberToChinese(detailInfo.amount) }}</el-descriptions-item>
						<el-descriptions-item label="付款方式">{{ detailInfo.payMethod }}</el-descriptions-item>
						<el-descriptions-item label="账户名">{{ detailInfo.accountName }}</el-descriptions-item>
						<el-descriptions-item label="账号">{{ detailInfo.accountNo }}</el-descriptions-item>
						<el-descriptions-item label="开户行">{{ detailInfo.openAccountBank }}</el-descriptions-item>
					</el-descriptions>

					<!-- 分割线 -->
					<el-divider style="margin: 20px 0">
						<span style="color: #909399; font-size: 14px">明细列表</span>
					</el-divider>

					<vxetable
						:height="'350'"
						v-if="detailVisible"
						:isNeedQueryApi="false"
						:isNeedPager="false"
						:remote="false"
						:isNeedTools="false"
						ref="table1"
						id="processAutomationZtcIndex20250726163711"
						:tableCols="tableCols1"
						:data="detailInfo.marketCostInitiateDetailList"
						showsummary
					>
					</vxetable>
				</div>
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted, nextTick } from 'vue';
import {
	GetMarketCostInitiateList,
	ExportMarketCostInitiateReviewList,
	GetMarketCostInitiateReviewApplyData,
	MarketCostInitiateModify,
	SendCwMarketCostInitiateApply,
} from '/@/api/bookkeeper/marketCostInitiate';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const uploadMf = defineAsyncComponent(() => import('/@/components/yhCom/uploadMf.vue'));
const shopSelect = defineAsyncComponent(() => import('/@/components/yhCom/shopSelect.vue'));
import { numberToChinese } from '/@/utils/tools';
import { ElMessage, FormInstance, ElMessageBox } from 'element-plus';
const refuploadMf = ref<InstanceType<typeof uploadMf> | null>(null);
const refuploadMfA = ref<InstanceType<typeof uploadMf> | null>(null);
const ruleFormRef = ref();
const initiateRef = ref();
import { debounce } from 'lodash-es';
const time = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
const checkBoxList = ref<any[]>([]);
const query = ref({
	startDate: time, //开始时间
	endDate: time, //结束时间
	status: null, //状态
	instanceId: '', //流程编号
	shopName: '', //店铺名称
	shopCode: '', //店铺编码
	initiateUserName: '', //发起人
});
const editVisible = ref(false);
const listLoading = ref(false);
const singleform = ref<{
	id: string | number;
	payPictureArr: string[];
	shopPictureArr: string[];
}>({
	id: '',
	payPictureArr: [],
	shopPictureArr: [],
});
const singlerules = ref({
	payPictureArr: [{ required: true, message: '请输入支付截图', trigger: 'blur' }],
	shopPictureArr: [{ required: true, message: '请输入店铺截图', trigger: 'blur' }],
});
const statusList = ref<Public.options[]>([
	{ label: '未发起', value: 0 },
	{ label: '待发起', value: 1 },
	{ label: '已发起', value: 2 },
	{ label: '已拒绝', value: 5 },
	{ label: '已撤销', value: 6 },
]);
const table = ref();
const loading = ref(false);

// 银行选项常量
const bankOptions = ref(['招商银行', '中国工商银行', '农商银行', '中信银行', '中国农业银行', '中国建设银行', '交通银行', '浙商银行', '其他（备注）', '中国银行', '中国邮政储蓄银行']);

const initiateVisible = ref(false);
const initiateLoading = ref(false);
const initiateform = ref({
	accountNo: '',
	accountName: '',
	openAccountBank: '',
	payTime: null as string | null,
	checkMarketCostIds: [] as (string | number)[],
});

const initiateRules = ref({
	accountNo: [{ required: true, message: '请输入收款账号', trigger: 'blur' }],
	accountName: [{ required: true, message: '请输入收款户名', trigger: 'blur' }],
	openAccountBank: [{ required: true, message: '请选择开户行', trigger: 'change' }],
	payTime: [{ required: true, message: '请输入支付时间', trigger: 'blur' }],
});

const detailVisible = ref(false);
const detailInfo = ref<any>({});

const initiateMethod = async () => {
	if (checkBoxList.value.length === 0) {
		ElMessage.error('请选择要发起的数据');
		return;
	}
	if (checkBoxList.value.some((item: any) => item.status === 2)) {
		ElMessage.error('勾选的项中包含已发起的数据，不能发起');
		return;
	}
	initiateVisible.value = true;
	await nextTick();
	initiateRef.value?.resetFields();
	initiateform.value.checkMarketCostIds = checkBoxList.value.map((item: any) => item.id);
	initiateform.value.payTime = checkBoxList.value[0].payTime ? dayjs(checkBoxList.value[0].payTime).format('YYYY-MM-DD HH:mm:ss') : dayjs().format('YYYY-MM-DD HH:mm:ss');
};

const initiateProps = async () => {
	if (!initiateRef.value) return;
	await initiateRef.value.validate(async (valid: any, fields: any) => {
		if (valid) {
			ElMessageBox.confirm('确定要发起营销费用吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(async () => {
					initiateLoading.value = true;
					const params = {
						...initiateform.value,
					};
					const { success } = await SendCwMarketCostInitiateApply(params);
					if (success) {
						ElMessage.success('发起成功');
						getList();
					}
					initiateLoading.value = false;
				})
				.catch(() => {
					initiateLoading.value = false;
				});
		}
	});
};

const exportProps = async () => {
	loading.value = true;
	await ExportMarketCostInitiateReviewList({ ...query.value, ...table.value.query })
		.then((data: any) => {
			loading.value = false;
			window.$message[data.success ? 'success' : 'error'](data.msg || (data.success ? '导出成功,稍后请到下载管理查看' : '导出失败'));
		})
		.catch(() => {
			loading.value = false;
		});
};

const onSingleSave = debounce(async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate(async (valid, fields) => {
		if (valid) {
			listLoading.value = true;
			const params = {
				...singleform.value,
				payPicture: singleform.value.payPictureArr.join(',') || '',
				shopPicture: singleform.value.shopPictureArr.join(',') || '',
				shopPictureArr: undefined,
				payPictureArr: undefined,
				marketCostId: singleform.value.id,
			};
			const { success } = await MarketCostInitiateModify(params);
			if (success) {
				ElMessage.success('编辑成功');
				editVisible.value = false;
				getList();
			}
			listLoading.value = false;
		}
	});
}, 500);

const handleClose = () => {
	editVisible.value = false;
	ruleFormRef.value.resetFields();
};

const handleEdit = (row: any) => {
	editVisible.value = true;
	singleform.value = { ...row };
	singleform.value.payPictureArr = row.payPicture.split(',');
	singleform.value.shopPictureArr = row.shopPicture.split(',');
};

const handleDetail = async (row: any) => {
	const { data, success } = await GetMarketCostInitiateReviewApplyData({ instanceId: row.instanceId });
	if (success) {
		detailInfo.value = data;
		detailVisible.value = true;
	}
};

const onCheckBoxMethod = (val: any) => {
	checkBoxList.value = val;
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.refreshTable(true);
	checkBoxList.value = [];
	table.value.clearSelection();
};

const tableCols1 = ref<VxeTable.Columns[]>([
	{ width: '100', field: 'pullDate', title: '拉取日期', align: 'center', formatter: 'formatDate' },
	{ width: '80', field: 'status', title: '状态', align: 'center', formatter: (row: any) => statusList.value.find((item) => item.value === row.status)?.label },
	{ width: '220', field: 'businessId', title: '流程编号', align: 'center' },
	{ width: '120', field: 'payTime', title: '支付时间', align: 'center' },
	{ width: '220', field: 'shopName', title: '店铺名称', align: 'center' },
	{ width: '120', field: 'shopCode', title: '店铺编码', align: 'center' },
	{ width: '120', field: 'shopId', title: '店铺ID', align: 'center' },
	{ width: '120', field: 'payPrice', title: '支付金额', align: 'right', formatter: 'fmtAmt2' },
	{ width: '100', field: 'payPicture', title: '支付截图', align: 'center', type: 'image' },
	{ width: '100', field: 'shopPicture', title: '店铺截图', align: 'center', type: 'image' },
	{ width: '120', field: 'initiateUserName', title: '发起人', align: 'center' },
	{ width: '120', field: 'initiateTime', title: '发起时间', align: 'center' },
]);

const tableCols = ref<VxeTable.Columns[]>([
	{ width: '100', sortable: true, field: 'pullDate', title: '拉取日期', align: 'center', formatter: 'formatDate' },
	{ width: '80', sortable: true, field: 'status', title: '状态', align: 'center', formatter: (row: any) => statusList.value.find((item) => item.value === row.status)?.label },
	{ width: '120', sortable: true, field: 'businessId', title: '流程编号', align: 'center' },
	{ width: '100', sortable: true, field: 'reviewStatusName', title: '财务审核状态', align: 'center' },
	{ width: '120', sortable: true, field: 'payTime', title: '支付时间', align: 'center' },
	{ width: '220', sortable: true, field: 'shopName', title: '店铺名称', align: 'center' },
	{ width: '90', sortable: true, field: 'shopCode', title: '店铺编码', align: 'center' },
	{ width: '90', sortable: true, field: 'shopId', title: '店铺ID', align: 'center' },
	{ width: '90', sortable: true, field: 'payPrice', title: '支付金额', align: 'right', formatter: 'fmtAmt2' },
	{ width: '100', field: 'payPicture', title: '支付截图', align: 'center', type: 'image' },
	{ width: '100', sortable: true, field: 'tranRemark', title: '支付摘要', align: 'center' },
	{ width: '100', field: 'shopPicture', title: '店铺截图', align: 'center', type: 'image' },
	{ width: '90', sortable: true, field: 'initiateUserName', title: '发起人', align: 'center' },
	{ width: '120', sortable: true, field: 'reviewRemark', title: '备注', align: 'center' },
	{ width: '120', sortable: true, field: 'initiateTime', title: '发起时间', align: 'center' },
	{
		title: '操作',
		align: 'center',
		width: '80',
		type: 'btnList',
		minWidth: '80',
		field: 'operation',
		btnList: [
			{
				title: '编辑',
				handle: handleEdit,
				isDisabled(row) {
					return row.reviewStatusName == '已审核' ? true : false;
				},
			},
			{ title: '详情', handle: handleDetail },
		],
		fixed: 'right',
	},
]);
</script>

<style scoped lang="scss"></style>
