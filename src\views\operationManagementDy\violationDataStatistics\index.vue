<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" class="demo-tabs w100 h100">
				<el-tab-pane label="违规链接下架" name="first" class="h100" v-auth="'LinkRemovedManagementPermission'">
					<linkRemoved :groupList="groupList" :directorlist="directorlist" />
				</el-tab-pane>
				<el-tab-pane label="违规词管理" name="second" class="h100" v-auth="'violationWordManagementPermission'" lazy>
					<violationWordManagement :groupList="groupList" :directorlist="directorlist" :shopList="shopList" />
				</el-tab-pane>
				<el-tab-pane label="白名单" name="third" class="h100" lazy>
					<whitelist :groupList="groupList" :directorlist="directorlist" :shopList="shopList" />
				</el-tab-pane>
			</el-tabs>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted } from 'vue';
const activeName = ref('first');
import { getDirectorGroupList, GetShopList, getDirectorList } from '/@/api/operatemanage/shop';
const linkRemoved = defineAsyncComponent(() => import('./components/linkRemoved.vue'));
const violationWordManagement = defineAsyncComponent(() => import('./components/violationWordManagement.vue'));
const whitelist = defineAsyncComponent(() => import('./components/whitelist.vue'));
const groupList = ref<any[]>([]);
const directorlist = ref<any[]>([]);
const shopList = ref<any[]>([]);
onMounted(async () => {
	const { data: data0, success: success0 } = await GetShopList({ platform: '', currentPage: 1, PageSize: 100000 });
	if (success0) {
		shopList.value = data0.list.map((item: any) => {
			return {
				value: item.shopCode,
				label: item.shopName,
				platform: item.platform,
			};
		});
	}
	const { data: data1, success: success1 } = await getDirectorGroupList();
	if (data1 && success1) {
		groupList.value = groupList.value.concat(
			data1.map((item: any) => {
				return { value: item.key, label: item.value };
			})
		);
	}
	const { data: data2, success: success2 } = await getDirectorList();
	if (data2 && success2) {
		directorlist.value = data2?.map((item: any) => {
			return {
				label: item.value,
				value: item.key,
			};
		});
	}
});
</script>

<style scoped lang="scss"></style>
