<template>
	<div style="width: 100%; height: 100%; text-align: center; display: flex; flex-direction: column; justify-content: center; align-items: center">
		<div style="width: 100%; flex: 87%; text-align: center">
			<!-- <div style="width: 68px; height: 68px; margin-top: 5px; border-radius: 5px; position: relative; background: red;margin-bottom: 5px; margin-right: 5px; border: 1px solid #eee;" v-for="(item,i) in state.form.warehousingPicList" :key="i">
                <el-image
                style="width: 100%; height: 100%"
                :src="item" :preview-src-list="state.form.warehousingPicList"></el-image>
                <div style="position: absolute; right: -3px; top: -3px; border-radius: 15px; width: 15px; height: 15px; background-color: red; display: flex; justify-content: center; align-items:center;">
                    <el-icon :size="15" @click="removeImg(item, i)">
                        <Close style="color: #fff;" />
                    </el-icon>
                </div>
            </div> -->
			<div style="display: flex; flex-direction: row">
				<Draggable v-model="state.form.warehousingPicList" key="index" :animation="500" class="list-group" :forceFallback="true" chosen-class="chosenClass" @end="endAble">
					<template #item="{ element }">
						<div
							:style="{
								width: `${computedWidth}px`,
								height: `${computedHeight}px`,
								marginTop: '5px',
								borderRadius: '5px',
								position: 'relative',
								background: 'red',
								marginBottom: '5px',
								marginRight: '5px',
								border: '1px solid #eee',
							}"
						>
							<el-image
								v-if="element.indexOf('.pdf') > -1"
								style="width: 100%; height: 100%"
								:initial-index="state.form.warehousingPicList.indexOf(element)"
								:hide-on-click-modal="true"
								:src="'https://nanc.yunhanmy.com:10010/media/video/20241103/1853027836488163329.png'"
							></el-image>
							<el-image
								v-else
								style="width: 100%; height: 100%"
								:initial-index="state.form.warehousingPicList.indexOf(element)"
								:hide-on-click-modal="true"
								:src="element"
								:preview-src-list="state.form.warehousingPicList"
							></el-image>
							<div
								style="
									position: absolute;
									cursor: pointer;
									right: -3px;
									top: -3px;
									border-radius: 15px;
									width: 15px;
									height: 15px;
									background-color: red;
									display: flex;
									justify-content: center;
									align-items: center;
								"
							>
								<el-icon :size="15" @click="removeImg(item, state.form.warehousingPicList.indexOf(element))">
									<Close style="color: #fff" />
								</el-icon>
							</div>
						</div>
					</template>
					<template #footer>
						<pasterMf
							v-if="limit > (state.form.warehousingPicList && state.form.warehousingPicList.length)"
							ref="refpasterMf"
							v-model:imagesStr="state.form.warehousingPicList"
							:upstyle="upstyle"
						></pasterMf>
					</template>
				</Draggable>
			</div>
		</div>

		<div style="width: 100%; flex: 13%">
			<el-upload
				v-if="limit > (state.form.warehousingPicList && state.form.warehousingPicList.length)"
				ref="warehousingImgBefore"
				class="upload-warehousing"
				action="warehousing"
				:accept="uploadFormat"
				:disbaled="disbaled"
				:limit="limit"
				:multiple="multiple"
				:file-list="fileList"
				:auto-upload="true"
				:show-file-list="false"
				:http-request="onImportTableAll"
			>
				<template #trigger>
					<!-- <el-icon size="16" style="" @click="onfileChange('warehousingImgBefore')"> <ele-Upload /> </el-icon> -->
					<el-button style="margin-top: 10px" :icon="Plus" :loading="state.uploading">{{ props.uploadName }}</el-button>
				</template>
				<!-- <el-icon size="16" @click="clearfile('warehousingImgBefore')" style="color: #F56C6C; margin-left: 5px;"> <ele-Delete /> </el-icon> -->
			</el-upload>
		</div>
	</div>
</template>

<script lang="ts" setup name="rosterIndex">
import Draggable from 'vuedraggable';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ref, reactive, onMounted, getCurrentInstance, onUnmounted, defineProps, defineAsyncComponent, computed, nextTick, toRaw, defineExpose, watch, inject, defineEmits } from 'vue';
const pasterMf = defineAsyncComponent(() => import('/@/components/yhCom/pasterMf.vue'));
import { Close } from '@element-plus/icons-vue';
const emit = defineEmits(['update:imagesStr']);
const refpasterMf = ref<InstanceType<typeof pasterMf> | null>(null);

const props = defineProps({
	disbaled: {
		type: Boolean,
		default: false,
	},
	multiple: {
		type: Boolean,
		default: false,
	},
	limit: {
		//限制长度
		type: Number,
		default: 9,
	},
	fileList: {
		type: Array,
		default: function () {
			return [];
		},
	},
	upstyle: {
		type: Object,
		default: () => ({ height: 65, width: 65 }),
	},
	imagesStr: {
		type: Array,
		default: function () {
			return []; //图片地址，数组格式
		},
	},
	uploadName: {
		type: String,
		default: '上传图片',
	},
	uploadFormat: {
		type: String,
		default: '.jpg,.png,.gif,.jpeg',
	},
	httpRequest: {
		type: Function,
		default: function () {
			return function () {
				console.log('httpRequest');
			};
		},
	},
});

const computedWidth = computed(() => props.upstyle.width || 65);
const computedHeight = computed(() => props.upstyle.height || 65);

const state = reactive({
	placetext: '',
	canvasimg: [],
	bigimg: '',
	isborder: false,
	showborder: true,
	keyarr: [],
	divshow: true,
	textshow: '',
	longto: false,

	//上传
	uploading: false,
	form: {
		warehousingPicList: [],
	},
	// 存储上传文件的详细信息
	uploadedFiles: [] as Array<{fileName: string, fileUrl: string}>,
});

watch(
	() => state.form.warehousingPicList,
	() => {
		emit('update:imagesStr', state.form.warehousingPicList);
	},
	{
		deep: true,
	}
);

watch(
	() => props.imagesStr,
	() => {
		state.form.warehousingPicList = props.imagesStr;
		console.log('imagesStr11111111', props.imagesStr);
	},
	{
		deep: true,
		immediate: true,
	}
);

// 清除图片数据
const clearMethod = () => {
	nextTick(() => {
		state.form.warehousingPicList = [];
		refpasterMf.value?.clearMethods();
	});
};

const onImportTableAll = async (filedata) => {
	// state.uploadname = name;
	if (filedata.action == 'pressure') {
		state.uploading = true;
	} else if (filedata.action == 'warehousing') {
		state.uploading = true;
	}
	console.log('打印上传', filedata);
	await AjaxFile(filedata.file, 0, '', filedata.action);
};

const AjaxFile = async (file: File, i: number, batchnumber, action: String) => {
	var name = file.name; //文件名
	var size = file.size; //总大小
	var shardSize = 2 * 1024 * 1024; //2m
	var shardCount = Math.ceil(size / shardSize); //总片数
	if (i >= shardCount) {
		return;
	}
	//计算每一片的起始与结束位置
	var start = i * shardSize;
	var end = Math.min(size, start + shardSize);

	//构造一个表单，FormData是HTML5新增的
	i = i + 1;
	var form = new FormData();
	form.append('data', file.slice(start, end)); //slice方法用于切出文件的一部分
	// form.append("data2", file); //slice方法用于切出文件的一部分

	form.append('batchnumber', batchnumber);
	form.append('fileName', name);
	form.append('total', shardCount); //总片数
	form.append('index', i); //当前是第几片

	axios({
		url: '/api/uploadnew/file/xmtvideouploadblockasync',
		method: 'POST',
		headers: {
			'content-type': 'multipart/form-data',
		},
		data: form,
	})
		.then((response) => {
			let res = response.data;
			if (res?.success) {
				state.uploadprogressPercentage = (i / shardCount).toFixed(2) * 100;
				if (i == shardCount) {
					// res.data.fileName = name;
					// res.data.uid = file.uid;
					// res.data.upLoadPhotoId = 0;
					let params = {
						fileName: name,
						fileUrl: res.data.url,
					};
					console.log('打印赋值参数u', params);
					// 保存文件详细信息
					state.uploadedFiles.push(params);
					// state.form[state.uploadname] = res.data.url;
					// state.uploadname = res.data.url
					// nextTick(async()=>{
					//   await getinfo();
					// })
					// if (action == "pressure") {
					//   if (!state.form.pressureOrderPicList) {
					//     state.form.pressureOrderPicList = [];
					//   }
					//   state.form.pressureOrderPicList.push(res.data.url);
					//   state.uploading = false;
					// } else if (action == "warehousing") {
					//   if (!state.form.warehousingPicList) {
					//     state.form.warehousingPicList = [];
					//   }
					state.form.warehousingPicList.push(res.data.url);
					state.uploading = false;
					// }
					ElMessage.success('上传完成！');
				} else {
					AjaxFile(file, i, res.data);
				}
			} else {
				if (action == 'pressure') {
					state.uploading = false;
				} else if (action == 'warehousing') {
					state.uploading = false;
				}
				ElMessage.error(res?.msg);
			}
		})
		.catch((error) => {
			if (action == 'pressure') {
				state.uploading = false;
			} else if (action == 'warehousing') {
				state.uploading = false;
			}
			console.error(error); // 处理错误
		});
};

const removeImg = (row, i) => {
	state.form.warehousingPicList.splice(i, 1);
};

const endAble = (arr) => {};

// 获取上传的文件信息（包括回显的文件）
const getUploadedFiles = () => {
	// 合并回显的文件和新上传的文件
	const echoFiles = state.form.warehousingPicList.map((url: string) => {
		// 从URL中提取文件名（简单处理）
		const fileName = url.split('/').pop() || 'unknown';
		return { fileName, fileUrl: url };
	});
	return echoFiles;
};

// 获取最新上传的文件信息
const getLatestUploadedFile = () => {
	const allFiles = getUploadedFiles();
	return allFiles.length > 0 ? allFiles[allFiles.length - 1] : null;
};

// 清空上传文件记录
const clearUploadedFiles = () => {
	state.uploadedFiles = [];
	state.form.warehousingPicList = [];
};

defineExpose({
	clearMethod,
	getUploadedFiles,
	getLatestUploadedFile,
	clearUploadedFiles,
});
</script>

<style lang="scss" scoped>
.list-group {
	display: flex;
	flex-direction: row;
	justify-content: start;
	flex-wrap: wrap;
}
.upload-warehousing {
	display: flex;
}
</style>
