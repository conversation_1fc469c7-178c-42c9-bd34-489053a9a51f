import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_OperateManage}/IllegalDeduction/`;

// 拼多多扣款申诉 - 查询
export const GetPddIllegalDeductionBadExperList = (params: any, config = {}) => {
    return request.post(apiPrefix + 'GetPddIllegalDeductionBadExperList', params, config);
};

// 拼多多扣款申诉 - 导出
export const ExportPddIllegalDeductionBadExperList = (params: any, config = {}) => {
    return request.post(apiPrefix + 'ExportPddIllegalDeductionBadExperList', params, config);
};

// 拼多多扣款申诉 - 申诉
export const SavePddIllegalDeductionOrderAppealInfo = (params: any, config = {}) => {
    return request.post(apiPrefix + 'SavePddIllegalDeductionOrderAppealInfo', params, config);
};

// 拼多多扣款申诉 - 申诉回显
export const GetPddIllegalDeductionOrderAppealInfoByOrderNo = (params: any, config = {}) => {
    return request.post(apiPrefix + 'GetPddIllegalDeductionOrderAppealInfoByOrderNo', params, config);
};

// 拼多多扣款申诉 - 配置查询
export const GetPddIllegalDeductionSetPageList = (params: any, config = {}) => {
    return request.post(apiPrefix + 'GetPddIllegalDeductionSetPageList', params, config);
};

// 拼多多扣款申诉 - 配置保存/修改
export const SavePddIllegalDeductionSet = (params: any, config = {}) => {
    return request.post(apiPrefix + 'SavePddIllegalDeductionSet', params, config);
};

// 拼多多扣款申诉 - 配置删除
export const DelPddIllegalDeductionSet = (params: any, config = {}) => {
    return request.post(apiPrefix + 'DelPddIllegalDeductionSet', params, config);
};

// 拼多多扣款申诉 - 获取聊天记录
export const GetPddIllegalDeductionOrderChatDataByOrderNo = (params: any, config = {}) => {
    return request.post(apiPrefix + 'GetPddIllegalDeductionOrderChatDataByOrderNo', params, config);
};
