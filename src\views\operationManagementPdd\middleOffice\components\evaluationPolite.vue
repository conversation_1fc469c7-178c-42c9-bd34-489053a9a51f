<template>
	<Container v-loading="loading">
		<template #header>
			<div class="topCss">
				<el-date-picker v-model="query.yearMonthDay" type="date" placeholder="请选择时间" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :clearable="false" style="width: 140px; margin: 0 5px 5px 0" />
				<el-select v-model="query.shopCode" placeholder="店铺" clearable filterable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false" class="public_Css">
					<el-option v-for="item in props.shopList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<el-input v-model.trim="query.proCode" placeholder="产品ID" clearable maxlength="50" class="publicCss_top" />
				<el-select v-model="query.groupId" placeholder="运营组" clearable filterable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false" class="public_Css">
					<el-option v-for="item in props.groupList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<el-select v-model="query.operateSpecialUserId" placeholder="运营专员" class="public_Css" filterable clearable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false">
					<el-option v-for="item in props.directorlist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.userId" placeholder="运营助理" class="public_Css" filterable clearable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false">
					<el-option v-for="item in props.directorlist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.status" placeholder="状态" class="publicCss" filterable clearable>
					<el-option v-for="item in statusList" :label="item.label" :value="item.value" :key="item.value" />
				</el-select>
				<div class="pb5">
					<el-button type="primary" @click="getList">查询</el-button>
					<el-button type="primary" @click="startImport">导入</el-button>
					<el-button type="primary" @click="exportProps">导出</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="evaluationPolite202507050939" :tableCols="tableCols" :query="query" :order-by="'yearMonthDay'" :is-Asc="false" :query-api="GetEvaluationIsPolitePageList" showsummary />
			<el-dialog title="导入数据" v-model="dialogVisible" width="30%" draggable overflow :close-on-click-modal="false" style="margin-top: -30vh !important">
				<template #header>
					<div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px">
						<div>导入数据</div>
						<div><el-button type="primary" @click="downFile">下载导入模版</el-button></div>
					</div>
				</template>
				<div style="height: 100px">
					<div style="display: flex; align-items: center; margin-bottom: 10px; gap: 10px">
						<span style="color: red">*</span>
						<el-date-picker v-model="yearMonthDay" type="date" placeholder="请选择时间" :clearable="false" style="width: 200px" />
					</div>
					<el-upload
						ref="uploadFile"
						class="upload-demo"
						:auto-upload="false"
						:multiple="false"
						:limit="1"
						action=""
						accept=".xlsx"
						:file-list="fileLists"
						:data="fileparm"
						:http-request="onUploadFile"
						:on-success="onUploadSuccess"
						:on-change="onUploadChange"
						:on-remove="onUploadRemove"
					>
						<template #trigger>
							<el-button size="small" type="primary">选取文件</el-button>
						</template>
						<el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="onSubmitUpload" v-reclick>{{ uploadLoading ? '上传中' : '上传' }}</el-button>
					</el-upload>
				</div>
				<div style="display: flex; justify-content: end; align-items: center">
					<el-button @click="dialogVisible = false">关闭</el-button>
				</div>
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, PropType } from 'vue';
import dayjs from 'dayjs';
import { GetEvaluationIsPolitePageList, ImportEvaluationIsPoliteAsync, ExportEvaluationIsPoliteAsync } from '/@/api/operatemanage/operate';
import { platformLink, downLoadFile } from '/@/utils/tools';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const props = defineProps({
	groupList: {
		type: Array as PropType<any[]>,
		default: () => [],
	},
	directorlist: {
		type: Array as PropType<any[]>,
		default: () => [],
	},
	shopList: {
		type: Array as PropType<any[]>,
		default: () => [],
	},
});
const query = ref({
	yearMonthDay: dayjs().format('YYYY-MM-DD'),
	groupId: [],
	shopCode: [],
	proCode: '',
	operateSpecialUserId: [],
	userId: [],
	status: null,
});
const statusList = ref([
	{ label: '已结束', value: '已结束' },
	{ label: '活动中', value: '活动中' },
]);
const yearMonthDay = ref<any>();
const dialogVisible = ref(false);
const fileLists = ref([]);
const fileparm = ref({});
const uploadLoading = ref(false);
const uploadFile = ref();
const table = ref();
const loading = ref(false);

const downFile = () => {
	downLoadFile('https://nanc.yunhanmy.com:10010/media/video/20250707/1942152067158614016.xlsx', '评价有礼导入模版.xlsx');
};

const exportProps = async () => {
	loading.value = true;
	await ExportEvaluationIsPoliteAsync({ ...query.value, ...table.value.query })
		.then((data: any) => {
			loading.value = false;
			const aLink = document.createElement('a');
			let blob = new Blob([data], { type: 'application/vnd.ms-excel' });
			aLink.href = URL.createObjectURL(blob);
			aLink.setAttribute('download', '评价有礼导出' + new Date().toLocaleString() + '.xlsx');
			aLink.click();
		})
		.catch(() => {
			loading.value = false;
		});
};

const onUploadRemove = (file: any, fileList: any) => {
	fileLists.value.splice(fileList.indexOf(file), 1);
};
const onUploadChange = async (file: any, fileList: any) => {
	fileLists.value.splice(0, fileList.length - 1);
	fileLists.value = fileList;
};
const onUploadSuccess = (response: any, file: any, fileList: any) => {
	fileLists.value.splice(fileList.indexOf(file), 1);
	fileLists.value = [];
	dialogVisible.value = false;
};
const onUploadFile = async (item: any) => {
	if (!item || !item.file || !item.file.size) {
		window.$message({ message: '请先上传文件', type: 'warning' });
		return false;
	}
	uploadLoading.value = true;
	const form = new FormData();
	form.append('upfile', item.file);
	form.append('yearMonthDay', dayjs(yearMonthDay.value).format('YYYY-MM-DD'));
	var res = await ImportEvaluationIsPoliteAsync(form);
	if (res?.success) window.$message({ message: res.data || '上传成功,正在导入中...', type: 'success' });
	uploadLoading.value = false;
	dialogVisible.value = false;
	getList();
};
const onSubmitUpload = () => {
	if (fileLists.value.length == 0) {
		window.$message({ message: '请先上传文件', type: 'warning' });
		return false;
	}
	uploadFile.value.submit();
};
const startImport = () => {
	fileLists.value = [];
	yearMonthDay.value = dayjs().subtract(1, 'day').toDate();
	dialogVisible.value = true;
};

const productMethod = (row: any) => {
	let providerLink = platformLink(row.platform, row.proCode);
	return `<a href="${providerLink}" target="_blank" style="color: #1000ff;text-decoration: none;">${row.proCode}</a>`;
};

const getList = async () => {
	table.value.refreshTable(true);
};
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, width: '100', align: 'center', field: 'yearMonthDay', title: '日期', formatter: (row: any) => dayjs(row.yearMonthDay).format('YYYY-MM-DD') },
	{ sortable: true, width: 'auto', align: 'center', field: 'shopName', title: '店铺' },
	{ sortable: true, width: '120', align: 'center', field: 'shopCode', title: '店铺ID' },
	{ sortable: true, width: '120', align: 'center', field: 'proCode', title: '产品ID', type: 'html', formatter: (row: any) => productMethod(row) },
	{ sortable: true, width: '100', align: 'center', field: 'groupId', title: '运营组', formatter: (row: any) => row.groupName || '' },
	{ sortable: true, width: '100', align: 'center', field: 'operateSpecialUserId', title: '运营专员', formatter: (row: any) => row.operateSpecialUserName || '' },
	{ sortable: true, width: '100', align: 'center', field: 'userId', title: '运营助理', formatter: (row: any) => row.userName || '' },
	{ sortable: true, width: '100', align: 'right', field: 'cashbackAmountMax', title: '最大返现金额', formatter: 'fmtAmt2' },
	{ sortable: true, width: '100', align: 'right', field: 'evaluationsWithImagesTotal', title: '带图评价总数' },
	{ sortable: true, width: '100', align: 'right', field: 'newEvaluationsWithImagesTotal', title: '新增带图评价数' },
	{ sortable: true, width: '100', align: 'right', field: 'evaluateConversionRate', title: '评价转换率' },
	{ sortable: true, width: '100', align: 'right', field: 'evaluateTotal', title: '评价总数' },
	{ sortable: true, width: '100', align: 'right', field: 'evaluationScoreRanking', title: '评价分排名' },
	{
		sortable: true,
		width: '100',
		align: 'right',
		field: 'storeEvaluationScoreRanking',
		title: '店铺评价分排名',
		formatter: (row: any) => (row.storeEvaluationScoreRanking ? row.storeEvaluationScoreRanking + '%' : ''),
	},
	{ sortable: true, width: '100', align: 'center', field: 'status', title: '状态' },
]);
</script>

<style scoped lang="scss">
::v-deep .el-select__tags-text {
	max-width: 60px;
}

.public_Css {
	width: 180px;
	margin: 0 5px 5px 0;
}
.publicCss_top {
	width: 140px;
	margin: 0 5px 5px 0;
}
</style>
