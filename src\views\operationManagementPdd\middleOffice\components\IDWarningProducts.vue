<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" v-model:startDate="query.startDate" v-model:endDate="query.endDate" style="width: 200px" :clearable="false" />
				<!-- <el-select class="publicCss" v-model="query.platformShopIDList" placeholder="店铺名称" clearable filterable multiple collapse-tags collapse-tags-tooltip>
					<el-option v-for="item in shopList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select> -->
				<shopSelect v-model:valueList="query.platformShopIDList" multiple field="platformShopID" class="publicCss" style="width: 290px"/>
				<el-select class="publicCss" v-model="query.groupIdList" placeholder="运营组" clearable filterable multiple collapse-tags collapse-tags-tooltip>
					<el-option label="无运营组" :value="0"> </el-option>
					<el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<el-select class="publicCss" v-model="query.operateSpecialUserIdList" placeholder="运营专员" clearable filterable multiple collapse-tags collapse-tags-tooltip>
					<el-option label="无运营专员" :value="0"> </el-option>
					<el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<el-select class="publicCss" v-model="query.userIdList" placeholder="运营助理" clearable filterable multiple collapse-tags collapse-tags-tooltip>
					<el-option label="无运营助理" :value="0"> </el-option>
					<el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<div class="numRangeCss">
					<div>商品质量排名</div>
					<numRange v-model:maxNum="query.goodsQltRankEnd" v-model:minNum="query.goodsQltRankStart" class="publicCss" minPlaceHolder="最小值" maxPlaceHolder="最大值" />
				</div>
				<div class="numRangeCss">
					<div>商品评价排名</div>
					<numRange v-model:maxNum="query.goodsRevRankEnd" v-model:minNum="query.goodsRevRankStart" class="publicCss" minPlaceHolder="最小值" maxPlaceHolder="最大值" />
				</div>
				<div class="numRangeCss">
					<div>负向反馈数</div>
					<numRange v-model:maxNum="query.negFbkCntEnd" v-model:minNum="query.negFbkCntStart" class="publicCss" minPlaceHolder="最小值" maxPlaceHolder="最大值" />
				</div>
				<div class="numRangeCss">
					<div>负向反馈率(%)</div>
					<numRange v-model:maxNum="query.negFbkRateEnd" v-model:minNum="query.negFbkRateStart" :precision="2" class="publicCss" minPlaceHolder="最小值" maxPlaceHolder="最大值" />
				</div>
				<div class="numRangeCss">
					<div>负向反馈率排名</div>
					<numRange v-model:maxNum="query.negFbkRateRankEnd" v-model:minNum="query.negFbkRateRankStart" class="publicCss" minPlaceHolder="最小值" maxPlaceHolder="最大值" />
				</div>
				<div class="numRangeCss">
					<div>差评数</div>
					<numRange v-model:maxNum="query.negRevCntEnd" v-model:minNum="query.negRevCntStart" class="publicCss" minPlaceHolder="最小值" maxPlaceHolder="最大值" />
				</div>
				<div class="numRangeCss">
					<div>差评率(%)</div>
					<numRange v-model:maxNum="query.negRevRateEnd" v-model:minNum="query.negRevRateStart" :precision="2" class="publicCss" minPlaceHolder="最小值" maxPlaceHolder="最大值" />
				</div>
				<el-input v-model.trim="query.goodsID" placeholder="产品ID" maxlength="50" clearable class="publicCss" />
				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				id="20250708105115"
				:tableCols="tableCols"
				showsummary
				isIndexFixed
				:query="query"
				:query-api="PageIDAspectListAsync"
				:export-api="ExportIDAspectListAsync"
				:asyncExport="{ title: 'ID维度预警商品', isAsync: false }"
			>
			</vxetable>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted } from 'vue';
import { getDirectorGroupList, GetShopList, getDirectorList } from '/@/api/operatemanage/shop';
import { PageIDAspectListAsync, ExportIDAspectListAsync } from '/@/api/operatemanage/operateTool';
import dayjs from 'dayjs';
import { formatters } from '/@/utils/vxetableFormats';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
const shopSelect = defineAsyncComponent(() => import('/@/components/yhCom/shopSelect.vue'));
import { platformlist } from '/@/utils/tools';
const groupList = ref<any[]>([]);
const shopList = ref<any[]>([]);
const directorlist = ref<Public.options[]>([]);
const query = ref({
	startDate: dayjs().format('YYYY-MM-DD'),
	endDate: dayjs().format('YYYY-MM-DD'),
	goodsID: '',
	groupIdList: [],
	operateSpecialUserIdList: [],
	userIdList: [],
	platformShopIDList: [],
	goodsQltRankStart: null,
	goodsQltRankEnd: null,
	goodsRevRankStart: null,
	goodsRevRankEnd: null,
	negFbkCntStart: null,
	negFbkCntEnd: null,
	negFbkRateStart: null,
	negFbkRateEnd: null,
	negFbkRateRankStart: null,
	negFbkRateRankEnd: null,
	negRevCntStart: null,
	negRevCntEnd: null,
	negRevRateStart: null,
	negRevRateEnd: null,
});
const table = ref();
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'gpDate', title: '日期', formatter: 'formatDate' },
	{ sortable: true, field: 'platformShopIDName', align: 'right', title: '店铺名称' },
	{ sortable: true, field: 'goodsID', title: '产品ID', align: 'right', type: 'html', formatter: (row: any) => formatters.formatLinkProcode(row.goodsID, 2) },
	{ sortable: true, field: 'groupId', title: '运营组', align: 'right', formatter: (row: any) => row.groupIdName },
	{ sortable: true, field: 'operateSpecialUserId', title: '运营专员', align: 'right', formatter: (row: any) => row.operateSpecialUserIdName },
	{ sortable: true, field: 'userId', title: '运营助理', align: 'right', formatter: (row: any) => row.userIdName },
	{ sortable: true, field: 'goodsQltRank', title: '商品质量排名', align: 'right' },
	{ sortable: true, field: 'goodsRevRank', title: '店铺评价排名', align: 'right' },
	{ sortable: true, field: 'negFbkCnt', title: '负向反馈数', align: 'right' },
	{ sortable: true, field: 'negFbkRate', title: '负向反馈率', align: 'right', formatter: 'fmtPercent' },
	{ sortable: true, field: 'negFbkRateRank', title: '负向反馈率排名', align: 'right', formatter: 'fmtPercent' },
	{ sortable: true, field: 'negRevCnt', title: '差评数', align: 'right' },
	{ sortable: true, field: 'negRevRate', title: '差评率', align: 'right', formatter: 'fmtPercent' },
	{ sortable: true, field: 'negRevRateRank', title: '差评率排名', align: 'right' },
]);
const getOperateUserName = async () => {
	const { data } = await getDirectorList();
	directorlist.value = data?.map((item: any) => {
		return {
			label: item.value,
			value: item.key,
		};
	});
};
onMounted(async () => {
	getOperateUserName();
	let { data, success } = await getDirectorGroupList();
	if (data && success) {
		groupList.value = groupList.value.concat(
			data.map((item: any) => {
				return { value: item.key, label: item.value };
			})
		);
	}
	const res = await GetShopList({ platform: 2, CurrentPage: 1, PageSize: 100000 });
	shopList.value = res.data.list.map((item: any) => {
		return {
			label: item.shopName,
			value: item.platformShopID,
		};
	});
});
</script>

<style scoped lang="scss">
.numRangeCss {
	display: flex;
	align-items: center;
	gap: 10px;
	margin: 0 5px 5px 0;
	font-size: 12px;
	.publicCss {
		margin-bottom: 0px;
	}
}

:deep(.el-select__tags-text) {
	max-width: 40px;
}
</style>
