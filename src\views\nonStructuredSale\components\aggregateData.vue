<template>
	<Container v-loading="loading">
		<template #header>
			<div class="topCss">
				<dataRange v-model:startDate="query.startDate" v-model:endDate="query.endDate" class="publicCss" style="width: 250px" :clearable="false" />
				<div class="pb5">
					<el-button type="primary" @click="getList">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<div class="aggregate-container">
				<div class="row-container">
					<!-- 每一行包含一个主架构师和其对应的子项 -->
					<div v-for="item in aggregateData" :key="item.architectureId" class="card-row" v-if="aggregateData.length > 0">
						<!-- 主架构师卡片 -->
						<el-card class="data-card main-card">
							<div class="card-title">{{ item.architecture }}</div>
							<div class="card-content">
								<div class="main-data">
									<div class="item-label">所有SKU:</div>
									<div class="item-value">{{ formatNumber(item.skuCount) }}</div>
								</div>
							</div>
						</el-card>

						<!-- 子项数据卡片 -->
						<el-card v-for="(subItem, index) in item.beishoumaiList" :key="`${item.architectureId}-${index}`" class="data-card sub-card">
							<div class="card-title">被{{ subItem.architecture }}售卖</div>
							<div class="card-content">
								<div class="sub-data-item">
									<div class="item-label">涉及售卖SKU:</div>
									<div class="item-value clickable" @click="handleDataItemClick('skuCount', subItem.beixuchenSkuCount, subItem.architecture)">
										{{ formatNumber(subItem.beixuchenSkuCount) }}
									</div>
								</div>
								<div class="sub-data-item">
									<div class="item-label">涉及ID:</div>
									<div class="item-value clickable" @click="handleDataItemClick('procodeCount', subItem.beixuchenProcodeCount, subItem.architecture)">
										{{ formatNumber(subItem.beixuchenProcodeCount) }}
									</div>
								</div>
								<div class="sub-data-item">
									<div class="item-label">涉及订单:</div>
									<div class="item-value clickable" @click="handleDataItemClick('orderNoCount', subItem.beixuchenOrderNoCount, subItem.architecture)">
										{{ formatNumber(subItem.beixuchenOrderNoCount) }}
									</div>
								</div>
							</div>
						</el-card>
					</div>
					<div v-else class="noData">暂无数据</div>
				</div>
			</div>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted } from 'vue';
import dayjs from 'dayjs';
import { GetNoJiaGouShouMaiGoodsSum } from '/@/api/bookkeeper/selectionProductCenterSaleDataPlanA';

// 定义数据类型接口
interface SubItem {
	architecture: string;
	beixuchenOrderNoCount: number;
	beixuchenProcodeCount: number;
	beixuchenSkuCount: number;
}

interface AggregateItem {
	architecture: string;
	architectureId: number;
	skuCount: number;
	beishoumaiList: SubItem[];
}
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const query = ref({
	startDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
	endDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
	isSale: 0,
});
const loading = ref(false);

// 模拟数据 - 按照新的数据结构
const aggregateData = ref<AggregateItem[]>([]);

// 千分位格式化函数
const formatNumber = (num: number): string => {
	return num === 0 || !num ? '0' : num.toLocaleString();
};

// 数据项点击事件
const handleDataItemClick = (type: string, value: number, architecture: string) => {
	console.log('点击了:', type, '值:', value, '架构师:', architecture);
	// 这里可以添加具体的点击处理逻辑，比如跳转到详情页面或显示弹窗
};

const getList = async () => {
	loading.value = true;
	try {
		const { data, success } = await GetNoJiaGouShouMaiGoodsSum(query.value);
		console.log(data, 'data');
		if (success && data.list) {
			// 如果API返回的是数组格式，直接使用
			if (Array.isArray(data.list)) {
				aggregateData.value = data.list;
			} else {
				// 如果API返回的是单个对象，转换为数组格式
				aggregateData.value = [data.list];
			}
		}
	} catch (error) {
		console.error('获取数据失败:', error);
	} finally {
		loading.value = false;
	}
};

onMounted(() => {
	getList();
});
</script>

<style scoped lang="scss">
.aggregate-container {
	padding: 20px;
	height: 100%;
	overflow-y: auto;
}

.row-container {
	display: flex;
	flex-direction: column;
	gap: 20px;
	max-width: 1200px;
	// margin: 0 auto; /* 居中 */
}

.card-row {
	display: flex;
	gap: 20px;
	align-items: stretch;

	.main-card {
		width: 385px; /* 主架构师卡片固定宽度 */
		flex-shrink: 0; /* 防止收缩 */
	}

	.sub-card {
		flex: 1;
		min-width: 0; /* 防止flex项目溢出 */
	}
}

.data-card {
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	transition: all 0.3s ease;

	&:hover {
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
		transform: translateY(-2px);
	}

	:deep(.el-card__body) {
		padding: 20px;
		height: 100%;
	}
}

.main-card {
	.card-content {
		text-align: center;

		.main-data {
			.item-label {
				font-size: 14px;
				color: #666;
				margin-bottom: 8px;
			}

			.item-value {
				font-size: 24px;
				font-weight: bold;
				color: #333;
			}
		}
	}
}

.sub-card {
	.card-content {
		.sub-data-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12px;

			&:last-child {
				margin-bottom: 0;
			}

			.item-label {
				font-size: 14px;
				color: #666;
				flex: 1;
			}

			.item-value {
				font-size: 16px;
				font-weight: bold;
				color: #333;
				text-align: right;

				&.clickable {
					color: #4a90e2;
					cursor: pointer;
					transition: color 0.3s ease;

					&:hover {
						color: #2c5aa0;
						text-decoration: underline;
					}
				}
			}
		}
	}
}

.card-title {
	font-size: 18px;
	font-weight: bold;
	color: #333;
	margin-bottom: 20px;
	text-align: center;
	border-bottom: 2px solid #f0f0f0;
	padding-bottom: 10px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
	.card-row {
		flex-wrap: wrap;

		.data-card {
			min-width: 300px;
		}
	}
}

@media (max-width: 768px) {
	.card-row {
		flex-direction: column;

		.data-card {
			min-width: auto;
		}
	}

	.aggregate-container {
		padding: 10px;
	}

	.row-container {
		gap: 15px;
	}
}

.noData {
	text-align: center;
	font-size: 16px;
	color: #999;
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 400px;
}
</style>
