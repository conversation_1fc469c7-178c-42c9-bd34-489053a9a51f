import request from '/@/utils/yhrequest';
// const apiPrefix = `http://**************:8000/api/bladegateway/yunhan-gis-personnel/`;
const apiPrefix = `${import.meta.env.VITE_APP_JAVA_API_BLADEGATEWAY}/yunhan-gis-personnel/`;

// const apiPrefix = `/yh-xz-salary/`;


//1、AI图片生成
export const ganerateImage = (params: any, config = {}) => request.post(apiPrefix + `jimengAI/ganerateImage`, params, config);

//生成图片历史记录查询接口
export const jimengAIGenerateImagePage = (params: any, config = {}) => request.post(apiPrefix + `jimengAI/jimengAIGenerateImagePage`, params, config);

//生成图片历史记录删除接口
export const jimengAIGenerateImageRemove = (params: any, config = {}) => request.post(apiPrefix + `jimengAI/jimengAIGenerateImageRemove?ids=`+params.ids, params, config);

//2、AI反推提示词
export const generatePrompt = (params: any, config = {}) => request.post(apiPrefix + `fastgpt/generatePrompt`, params, config);

//历史记录查询接口
export const cozeImageToPromptPage = (params: any, config = {}) => request.post(apiPrefix + `fastgpt/cozeImageToPromptPage`, params, config);

// 3、AI添加文字
export const cozeWorkFlowChat = (params: any, config = {}) => request.post(apiPrefix + `data-analysis/cozeWorkFlowChat`, params, config);

//生成图片历史记录删除接口
export const cozeImageToPromptRemove = (params: any, config = {}) => request.post(apiPrefix + `fastgpt/cozeImageToPromptRemove?ids=`+params.ids, params, config);

//修图上传
export const uploadImage = (params: any, config = {}) => request.post(apiPrefix + `comfyUI/uploadImage`, params, config);

//修图上传
export const comfyUIFixImageSubmit = (params: any, config = {}) => request.post(apiPrefix + `comfyUI/comfyUIFixImageSubmit`, params, config);

//查询
export const comfyUIFixImagePage = (params: any, config = {}) => request.post(apiPrefix + `comfyUI/comfyUIFixImagePage`, params, config);

//删除
export const comfyUIFixImageRemove = (params: any, config = {}) => request.post(apiPrefix + `comfyUI/comfyUIFixImageRemove?ids=`+params.ids, params, config);