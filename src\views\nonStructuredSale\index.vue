<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" class="demo-tabs w100 h100">
				<el-tab-pane label="汇总数据" name="first" class="h100" lazy>
					<aggregateData />
				</el-tab-pane>
				<el-tab-pane label="明细数据" name="second" class="h100" lazy>
					<detailData :shopList="shopList" :groupList="groupList" :directorlist="directorlist" />
				</el-tab-pane>
			</el-tabs>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted } from 'vue';
const activeName = ref('first');
const aggregateData = defineAsyncComponent(() => import('./components/aggregateData.vue'));
const detailData = defineAsyncComponent(() => import('./components/detailData.vue'));
import { getDirectorGroupList, GetShopList, getDirectorList } from '/@/api/operatemanage/shop';
const groupList = ref<any[]>([{ value: 0, label: '空白' }]);
const shopList = ref<any[]>([]);
const directorlist = ref<any[]>([]);
onMounted(async () => {
	//店铺
	const { data: data0, success: success0 } = await GetShopList({ platform: '', currentPage: 1, PageSize: 100000 });
	if (success0) {
		shopList.value = data0.list.map((item: any) => {
			return {
				value: item.shopCode,
				label: item.shopName,
				platform: item.platform,
			};
		});
	}
	//运营组
	const { data: data1, success: success1 } = await getDirectorGroupList();
	if (data1 && success1) {
		groupList.value = groupList.value.concat(
			data1.map((item: any) => {
				return { value: item.key, label: item.value };
			})
		);
	}
	//运营专员
	const { data: data2, success: success2 } = await getDirectorList();
	if (data2 && success2) {
		directorlist.value = data2?.map((item: any) => {
			return {
				label: item.value,
				value: item.key,
			};
		});
	}
});
</script>

<style scoped lang="scss"></style>
