<template>
	<Container v-loading="loading">
		<template #header>
			<div class="topCss">
				<el-select v-model="query.personnelIds" placeholder="人员名称" class="public_Css" filterable clearable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false">
					<el-option v-for="item in props.directorlist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input v-model.trim="query.proCode" placeholder="产品ID" clearable maxlength="50" class="publicCss" />
				<el-select v-model="query.shopCodes" placeholder="店铺" class="public_Css" clearable filterable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false">
					<el-option v-for="item in props.shopList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input v-model.trim="query.shopCode" placeholder="店铺ID" clearable maxlength="50" class="publicCss" />
				<el-select v-model="query.createdUserId" placeholder="创建人" class="public_Css" filterable clearable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false">
					<el-option v-for="item in props.directorlist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<div class="pb5">
					<el-button type="primary" @click="getList">查询</el-button>
					<el-button type="primary" @click="onAdd">新增</el-button>
					<el-button type="primary" @click="startImport">导入</el-button>
					<el-button type="primary" @click="exportProps">导出</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				id="personnelConfiguration202506271119"
				:tableCols="tableCols"
				:query="query"
				:is-Asc="false"
				:query-api="EditingPersonnelConfigurationQuery"
				showsummary
				:tree-config="{ children: 'children' }"
			>
				<template #rightCols>
					<vxe-column title="操作" width="100" fixed="right" align="center" field="202506271656">
						<template #default="{ row }">
							<div style="display: flex; justify-content: center">
								<el-button type="text" v-if="!(row.children && row.children.length >= 0)" @click="onEditMethod(row)">编辑</el-button>
								<el-button type="text" @click="onDeleteMethod(row)" style="color: red">删除</el-button>
							</div>
						</template>
					</vxe-column>
				</template>
			</vxetable>
			<el-dialog title="导入数据" v-model="dialogVisible" width="30%" draggable overflow :close-on-click-modal="false" style="margin-top: -30vh !important">
				<div style="height: 100px">
					<el-upload
						ref="uploadFile"
						class="upload-demo"
						:auto-upload="false"
						:multiple="false"
						:limit="1"
						action=""
						accept=".xlsx"
						:file-list="fileLists"
						:data="fileparm"
						:http-request="onUploadFile"
						:on-success="onUploadSuccess"
						:on-change="onUploadChange"
						:on-remove="onUploadRemove"
					>
						<template #trigger>
							<el-button size="small" type="primary">选取文件</el-button>
						</template>
						<el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="onSubmitUpload" v-reclick>{{ uploadLoading ? '上传中' : '上传' }}</el-button>
					</el-upload>
				</div>
				<div style="display: flex; justify-content: end; align-items: center">
					<el-button @click="dialogVisible = false">关闭</el-button>
				</div>
			</el-dialog>
			<el-dialog :title="isAdd ? '新增' : '编辑'" v-model="addVisible" width="20%" draggable overflow :close-on-click-modal="false" style="margin-top: -30vh !important">
				<div :style="isAdd ? 'height: 120px' : 'height: 110px; padding-top: 20px'">
					<el-form :model="addForm" ref="addFormRef" :rules="addRules" label-width="100px">
						<el-form-item label="人员名称" prop="personnelId" v-if="isAdd">
							<el-select v-model="addForm.personnelId" placeholder="请选择人员名称" style="width: 100%" filterable clearable>
								<el-option v-for="item in props.directorlist" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>
						<el-form-item label="产品ID" prop="proCode">
							<el-input v-model="addForm.proCode" placeholder="请输入产品ID" clearable maxlength="50" style="width: 100%" />
						</el-form-item>
					</el-form>
				</div>
				<div style="display: flex; justify-content: end; align-items: center">
					<el-button @click="addVisible = false">关闭</el-button>
					<el-button type="primary" @click="onSubmitAdd">提交</el-button>
				</div>
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, PropType, nextTick } from 'vue';
import dayjs from 'dayjs';
import {
	EditingPersonnelConfigurationQuery,
	ImportEditingPersonnelConfiguration,
	ExportEditingPersonnelConfiguration,
	EditingPersonnelConfigurationAdd,
	EditingPersonnelConfigurationEdit,
	EditingPersonnelConfigurationDelete,
} from '/@/api/operatemanage/productManager';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
import { ElMessage, ElMessageBox } from 'element-plus';
const props = defineProps({
	shopList: {
		type: Array as PropType<any[]>,
		default: () => [],
	},
	directorlist: {
		type: Array as PropType<any[]>,
		default: () => [],
	},
});
const query = ref({
	personnelIds: [], //人员名称
	proCode: '', //产品ID
	shopCodes: [], //店铺
	shopCode: '', //店铺ID
	createdUserId: [], //创建人
});
const dialogVisible = ref(false);
const fileLists = ref([]);
const fileparm = ref({});
const uploadLoading = ref(false);
const uploadFile = ref();
const table = ref();
const loading = ref(false);

const isAdd = ref(false);
const addVisible = ref(false);
const addForm = ref({
	personnelId: '',
	proCode: '',
	id: '',
});
const addFormRef = ref();
const addRules = ref({
	personnelId: [{ required: true, message: '请选择人员名称', trigger: 'blur' }],
	proCode: [{ required: true, message: '请输入产品ID', trigger: 'blur' }],
});

const onSubmitAdd = () => {
	addFormRef.value.validate(async (valid: any) => {
		if (valid) {
			let success = false;
			let params = { ...addForm.value };
			if (isAdd.value) {
				await EditingPersonnelConfigurationAdd(params).then((res: any) => {
					success = res.success;
				});
			} else {
				const { personnelId, ...body } = params;
				await EditingPersonnelConfigurationEdit(body).then((res: any) => {
					success = res.success;
				});
			}
			if (success) {
				window.$message({ message: '操作成功', type: 'success' });
				addVisible.value = false;
				getList();
			}
		}
	});
};

const onAdd = () => {
	addVisible.value = true;
	nextTick(() => {
		clearVerification();
		addForm.value.proCode = '';
		isAdd.value = true;
	});
};

const clearVerification = () => {
	if (addFormRef.value) {
		addFormRef.value?.resetFields();
		addFormRef.value?.clearValidate();
	}
	addForm.value.personnelId = '';
};

const onEditMethod = (row: any) => {
	addVisible.value = true;
	nextTick(() => {
		clearVerification();
		addForm.value.id = row.id;
		addForm.value.proCode = row.proCode || '';
		isAdd.value = false;
	});
};

const onDeleteMethod = (row: any) => {
	ElMessageBox.confirm('确定删除该剪辑人员配置吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		await EditingPersonnelConfigurationDelete({ id: row.id, deleteType: !(row.children && row.children.length >= 0) ? 2 : 1 }).then((res: any) => {
			if (res.success) {
				window.$message({ message: '删除成功', type: 'success' });
				getList();
			}
		});
	});
};

const exportProps = async () => {
	loading.value = true;
	await ExportEditingPersonnelConfiguration({ ...query.value, ...table.value.query })
		.then((data: any) => {
			loading.value = false;
			const aLink = document.createElement('a');
			let blob = new Blob([data], { type: 'application/vnd.ms-excel' });
			aLink.href = URL.createObjectURL(blob);
			aLink.setAttribute('download', '剪辑人员配置导出' + new Date().toLocaleString() + '.xlsx');
			aLink.click();
		})
		.catch(() => {
			loading.value = false;
		});
};

const onUploadRemove = (file: any, fileList: any) => {
	fileLists.value.splice(fileList.indexOf(file), 1);
};
const onUploadChange = async (file: any, fileList: any) => {
	fileLists.value.splice(0, fileList.length - 1);
	fileLists.value = fileList;
};
const onUploadSuccess = (response: any, file: any, fileList: any) => {
	fileLists.value.splice(fileList.indexOf(file), 1);
	fileLists.value = [];
	dialogVisible.value = false;
};
const onUploadFile = async (item: any) => {
	if (!item || !item.file || !item.file.size) {
		window.$message({ message: '请先上传文件', type: 'warning' });
		return false;
	}
	uploadLoading.value = true;
	const form = new FormData();
	form.append('upfile', item.file);
	var res = await ImportEditingPersonnelConfiguration(form);
	if (res?.success) window.$message({ message: res.data || '上传成功,正在导入中...', type: 'success' });
	uploadLoading.value = false;
	dialogVisible.value = false;
	getList();
};
const onSubmitUpload = () => {
	if (fileLists.value.length == 0) {
		window.$message({ message: '请先上传文件', type: 'warning' });
		return false;
	}
	uploadFile.value.submit();
};
const startImport = () => {
	fileLists.value = [];
	dialogVisible.value = true;
};

const getList = async () => {
	table.value.refreshTable(true);
};
const tableCols = ref<VxeTable.Columns[]>([
	{ width: 'auto', field: 'personnelName', title: '人员名称', align: 'center', treeNode: true },
	{ width: 'auto', field: 'groupName', title: '组', align: 'center' },
	{ width: 'auto', field: 'proCode', title: '产品ID', align: 'center' },
	{ width: 'auto', field: 'shopName', title: '店铺', align: 'center' },
	{ width: 'auto', field: 'shopCode', title: '店铺ID', align: 'center' },
	{ width: 'auto', field: 'createdUserName', title: '创建人', align: 'center' },
	{ width: 'auto', field: 'createdTime', title: '创建时间', align: 'center' },
]);
</script>

<style scoped lang="scss">
.public_Css {
	width: 185px;
	margin: 0 10px 5px 0;
}

::v-deep .el-select__tags-text {
	max-width: 70px;
}
</style>
