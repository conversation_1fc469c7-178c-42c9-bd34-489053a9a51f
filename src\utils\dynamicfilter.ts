import dayjs from 'dayjs';
import request from '/@/utils/yhrequest';
export default class DynamicFilterService {
	constructor(private apiUrl = '/api/verifyOrder/Orders/RealTime/GetWhere') {}
	
	async getField(summaryItem: DynamicFilter.summariesItem) {
		let field = '';
		const arr = ['Count( ', 'CountIf( ', 'Count(Distinct ', 'CountIf(Distinct '];

		if (summaryItem.summaryItems && summaryItem.summaryItems.length > 0) {
			for (const element of summaryItem.summaryItems) {
				let value = await this.getItemValue(element, arr.includes(element.value!));
				field += value;
			}
		}
		return field;
	}

	async getItemValue(item: DynamicFilter.summaryItem, isCount: boolean) {
		let value = '';

		if (item.prefix) {
			value += item.prefix;
		}

		if (item.type === 0) {
			value += 'Cast(';
		}

		if (item.value) {
			value += item.value;
		}

		if (item.summaryItems && item.summaryItems.length > 0) {
			for (const element of item.summaryItems) {
				const index = item.summaryItems!.findIndex((a: DynamicFilter.summaryItem) => a === element);
				const addOperator = isCount && !item.prefix && item.summaryItems.length > 1 && index != item.summaryItems.length - 1;
				value += (await this.getItemValue(element, isCount)) + (addOperator ? ',' : ' ');
			}
		}

		if (item.condition && this.isDynamicWhere(item.condition)) {
			let where = await this.getWhere(item.condition);
			value += ',' + where;
		} else if (item.conditionWhere) {
			value += ',' + item.conditionWhere;
		}

		if (item.type === 0) {
			value += ')';
			value += ' as decimal(38,4))';
		}

		if (item.suffix) {
			value += item.suffix;
		}

		return value;
	}

	isDynamicWhere(condition: DynamicFilter.condition): boolean {
		if (!condition || (condition.filters?.length === 0 && !condition.operator)) {
			return false;
		}
		const arr = ['PreHours', 'PreDays', 'PreMonths', 'CurrentDay', 'CurrentWeek', 'CurrentMonth', 'IsEmpty', 'NotEmpty'];
		let result = arr.includes(condition.operator!);

		if (condition.filters && condition.filters.length > 0) {
			for (const element of condition.filters) {
				result = this.isDynamicWhere(element);
				if (result === true) {
					return true;
				}
			}
		}
		return result;
	}

	isDynamicWhere2(summaryItem: DynamicFilter.summariesItem): boolean {
		return summaryItem.summaryItems!?.some((item: DynamicFilter.summaryItem) => {
			return this.isDynamicWhere(item.condition!);
		});
	}

	repalceFilter(dynamicValue: DynamicFilter.condition) {
		const res = JSON.parse(JSON.stringify(dynamicValue));
		const arr = ['PreHours', 'PreDays', 'PreMonths', 'CurrentDay', 'CurrentWeek', 'CurrentMonth', 'IsEmpty', 'NotEmpty'];
		const map = {
			PreHours: (val: number) => {
				return [dayjs().subtract(val, 'hour').format('YYYY-MM-DD HH:mm:ss'), dayjs().format('YYYY-MM-DD HH:mm:ss')];
			},
			PreDays: (val: number) => {
				return [dayjs().subtract(val, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
			},
			PreMonths: (val: number) => {
				return [dayjs().subtract(val, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
			},
			CurrentDay: () => {
				return [dayjs().startOf('day').format('YYYY-MM-DD'), dayjs().endOf('day').add(1, 'day').format('YYYY-MM-DD')];
			},
			CurrentWeek: () => {
				return [dayjs().startOf('week').format('YYYY-MM-DD'), dayjs().endOf('week').add(1, 'day').format('YYYY-MM-DD')];
			},
			CurrentMonth: () => {
				return [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').add(1, 'day').format('YYYY-MM-DD')];
			},
			IsEmpty: () => {
				return '';
			},
			NotEmpty: () => {
				return '';
			},
		} as any;
		const timearr = ['PreHours', 'PreDays', 'PreMonths', 'CurrentDay', 'CurrentWeek', 'CurrentMonth'];
		const noneArr = ['IsEmpty', 'NotEmpty'];
		res.filters?.forEach((item: DynamicFilter.condition) => {
			if (arr.includes(item.operator!)) {
				item.value = map[item.operator!](item.value);
				if (timearr?.includes(item.operator!)) {
					item.operator = 'Range';
				} else if (noneArr?.includes(item.operator!)) {
					item.operator = item.operator === 'IsEmpty' ? 'Equal' : 'NotEqual';
					item.value = '';
				}
			}
		});
		return res;
	}

	getWhere = async (condition: DynamicFilter.condition) => {
		const { data } = await request.post(this.apiUrl, { dynamicFilter: this.repalceFilter(condition) });
		return data;
	};
}
