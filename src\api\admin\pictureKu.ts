import request from '/@/utils/yhrequest';
const apiPrefix = `/api/xzgateway/yh-xz-salary/`;
// const apiPrefix = `/yh-xz-salary/`;


//图库分页
export const imageGalleryPage = (params: any, config = {}) => request.post(apiPrefix + `imageGalleryPage`, params, config);

//图库创建
export const imageGallerySubmit = (params: any, config = {}) => request.post(apiPrefix + `imageGallerySubmit`, params, config);


//导出
export const imageGalleryExport = (params: any, config: any = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'imageGalleryExport', params, config)
}

//导出下载量
export const imageGalleryExportDownloadCount = (params: any, config: any = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'imageGalleryExportDownloadCount', params, config)
}

//导出上传量
export const imageGalleryExportUploadCount = (params: any, config: any = { responseType: 'blob' }) => {
    return request.post(apiPrefix + 'imageGalleryExportUploadCount', params, config)
}

//批量删除
export const imageGalleryRemove = (params: any, config = {}) => request.post(apiPrefix + `imageGalleryRemove?ids=`+params.ids, params, config);

//详情
export const imageGalleryDetail = (params: any, config = {}) => request.post(apiPrefix + `imageGalleryDetail`, params, config);


//查到类似
export const imageGalleryByStyleCode = (params: any, config = {}) =>
    request.post(apiPrefix + `imageGalleryByStyleCode?styleCode=` + params.styleCode + '&imageType=' + params.imageType+ '&id=' + params.id, params, config);

//更新下载量
export const imageGalleryUpdateDownloadCount = (params: any, config = {}) =>
    request.post(apiPrefix + `imageGalleryUpdateDownloadCount?id=${params.id}&downloadCount=${params.downloadCount}`, config);

//更新浏览量
export const imageGalleryUpdateViewCount = (params: any, config = {}) =>
    request.post(apiPrefix + `imageGalleryUpdateViewCount?id=${params.id}&viewCount=${params.viewCount}`, config);

export const imageGalleryDownload = (params: any, config = {}) =>
    request.post(apiPrefix + `imageGalleryDownload?id=${params.id}`, config);

//批量更新
export const imageGalleryBatchUpdate = (params: any, config = {}) =>
    request.post(apiPrefix + `imageGalleryBatchUpdate?ids=${params.ids}`, params, config);

//批量还原
export const imageGalleryBatchRestore = (params: any, config = {}) =>
    request.post(apiPrefix + `imageGalleryBatchRestore?ids=${params.ids}`, params, config);

//获取图库统计数据
export const imageGalleryStats = (params: any, config = {}) =>
    request.post(apiPrefix + `imageGalleryStats`, params, config);

//获取相关图片
export const imageGalleryRelated = (params: any, config = {}) =>
    request.post(apiPrefix + `imageGalleryRelated?productCode=${params.productCode}&imageType=${params.imageType}`, params, config);

//增强型搜索
export const imageGalleryAdvancedSearch = (params: any, config = {}) =>
    request.post(apiPrefix + `imageGalleryAdvancedSearch`, params, config);

export const getImageGalleryRecordStats = (params: any, config = {}) =>
    request.post(apiPrefix + `getImageGalleryRecordStats`, params, config);

export const getSystemStats = (config = {}) =>
    request.get(apiPrefix + `fileChunk/getSystemStats`, config);

export const getUploadQueueInfo = (config = {}) =>
    request.get(apiPrefix + `fileChunk/getUploadQueueInfo`, config);

export const getAllUploadingFilesDetail = (config = {}) =>
    request.get(apiPrefix + `fileChunk/getAllUploadingFilesDetail`, config);

// 检查文件分片状态
export const checkChunk = (identifier: string, config = {}) =>
    request.get(apiPrefix + `fileChunk/checkChunk?identifier=${identifier}`, config);

// 上传文件分片
export const uploadChunk = (data: FormData, config = {}) =>
    request.post(apiPrefix + `fileChunk/uploadChunk`, data, {
        ...config,
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        transformRequest: []
    });

/**
 * 获取按上传人统计的上传量趋势图数据
 * @param params
 * @returns
 */
export const getUploadCountByUser = (params: any) => {
  return request.post(apiPrefix + 'getUploadCountByUser', params);
}
