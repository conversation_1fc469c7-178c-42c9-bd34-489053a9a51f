<template>
  <div class="tab">
    <el-form :model="formModel" ref="tableForm">
      <div class="btn">
        <el-button type='primary' link @click='addRow'>新增行</el-button>
      </div>
      <div class="table-container">
        <el-table
            :data="tableData"
            stripe
            border
            header-cell-class-name='x-table-header'
            cell-class-name='x-table-cell'
            row-key="id"
            style="width: 100%"
            height="410"
        >
          <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>

          <el-table-column prop="dutyType" label="类型" align="center" show-overflow-tooltip>
            <template #default="{row, $index}">
              <el-form-item
                  v-if="row.isEditor"
                  :prop="`tableData.${$index}.dutyType`"
                  :rules="rules.dutyType"
              >
                <el-input
                    v-model="row.dutyType"
                    maxlength="30"
                />
              </el-form-item>
              <div v-else class="text-ellipsis">
                {{ row.dutyType }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="dutyDept" label="部门" align="center" show-overflow-tooltip>
            <template #default="{row, $index}">
              <el-form-item
                  v-if="row.isEditor"
                  :prop="`tableData.${$index}.dutyDept`"
                  :rules="rules.dutyDept"
              >
                <el-input
                    v-model="row.dutyDept"
                    maxlength="30"
                />
              </el-form-item>
              <div v-else class="text-ellipsis">
                {{ row.dutyDept }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="keyWord" label="关键字" align="center" show-overflow-tooltip>
            <template #default="{row, $index}">
              <el-form-item
                  v-if="row.isEditor"
                  :prop="`tableData.${$index}.keyWord`"
                  :rules="rules.keyWord"
              >
                <el-input
                    v-model="row.keyWord"
                    maxlength="30"
                />
              </el-form-item>
              <div v-else class="text-ellipsis">
                {{ row.keyWord }}
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" width="180">
            <template #default="{row, $index}">
              <div class="action-buttons">
                <el-button
                    type='primary'
                    text
                    :disabled="!!editingRow && editingRow.id !== row.id"
                    @click="editRow(row, $index)"
                >
                  {{ row.isEditor ? '取消' : '编辑' }}
                </el-button>

                <el-button
                    type="primary"
                    text
                    v-if="row.isEditor"
                    @click="saveRow(row, $index)"
                >
                  保存
                </el-button>

                <el-button
                    type="danger"
                    text
                    :disabled="row.isEditor"
                    @click="delRow(row, $index)"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>

        </el-table>
      </div>
      <!-- 分页控件 -->
      <div class="pagination-container">
        <div class="pagination" v-if="total > 0">
          <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import {
  DelPddIllegalDeductionSet,
  GetPddIllegalDeductionSetPageList,
  SavePddIllegalDeductionSet
} from "/@/api/operatemanage/pddDeductionAppeal";

// 表单模型和校验规则
const formModel = reactive({ tableData: [] as any[] });
const tableForm = ref<FormInstance>();
const rules = reactive<FormRules>({
  dutyType: [
    { required: true, message: '请输入类型', trigger: 'blur' },
    { max: 30, message: '长度不能超过30个字符', trigger: 'blur' }
  ],
  dutyDept: [
    { required: true, message: '请输入部门', trigger: 'blur' },
    { max: 30, message: '长度不能超过30个字符', trigger: 'blur' }
  ],
  keyWord: [
    { required: true, message: '请输入关键字', trigger: 'blur' },
    { max: 30, message: '长度不能超过30个字符', trigger: 'blur' }
  ],
});

// 表格数据和编辑状态
const tableData = ref<any[]>([]);
const editingRow = ref<any>(null); // 当前正在编辑的行
const total = ref(0); // 总条数

// 分页参数
const pagination = reactive({
  currentPage: 1,
  pageSize: 10
});

// 初始化加载数据
onMounted(() => {
  loadTableData();
});

// 重置编辑状态
const resetEditingState = () => {
  editingRow.value = null;
  tableData.value.forEach(row => row.isEditor = false);
}

// 加载表格数据
const loadTableData = async () => {
  try {
    const response = await GetPddIllegalDeductionSetPageList({
      currentPage: pagination.currentPage,
      pageSize: pagination.pageSize,
      orderBy: 'createdTime',
      isAsc: false
    });
    const { list, total: totalCount } = response.data || {};
    tableData.value = (list || []).map(item => ({
      ...item,
      isEditor: false
    }));
    formModel.tableData = [...tableData.value];
    total.value = totalCount || 0;
  } catch (error) {
    console.error('数据加载失败:', error);
    ElMessage.error('数据加载失败');
  }
};

// 分页大小改变
const handleSizeChange = (size: number) => {
  resetEditingState(); // 翻页时重置编辑状态
  pagination.pageSize = size;
  pagination.currentPage = 1;
  loadTableData();
};

// 页码改变
const handleCurrentChange = (page: number) => {
  resetEditingState(); // 翻页时重置编辑状态
  pagination.currentPage = page;
  loadTableData();
};

// 新增行（添加到第一行）
const addRow = () => {
  if (editingRow.value) {
    ElMessage.warning('请先完成当前编辑');
    return;
  }

  const newRow = {
    id: Date.now(), // 临时ID
    dutyType: "",
    dutyDept: "",
    keyWord: "",
    isEditor: true
  };

  tableData.value.unshift(newRow); // 修改为添加到第一行
  formModel.tableData = [...tableData.value];
  editingRow.value = newRow;
};

// 编辑行
const editRow = (row: any, index: number) => {
  if (editingRow.value) {
    // 如果是取消当前行的编辑
    if (editingRow.value.id === row.id) {
      // 取消编辑时刷新列表
      if (typeof row.id === 'number' && row.id > 100000) {
        // 新增行取消编辑，直接删除
        tableData.value.splice(index, 1);
      } else {
        // 已有行取消编辑，刷新列表
        loadTableData();
      }
      editingRow.value = null;
      return;
    }
    ElMessage.warning('请先完成当前编辑');
    return;
  }

  row.isEditor = true;
  editingRow.value = row;
};

// 保存行
const saveRow = async (row: any, index: number) => {
  if (!tableForm.value) return;

  try {
    // 验证当前行
    await tableForm.value.validateField(`tableData.${index}`);

    const { id, isEditor, ...payload } = row;
    const res = await SavePddIllegalDeductionSet({
      ...payload,
      type: 0,
      id: typeof id === 'number' && id > 100000 ? undefined : id // 移除临时ID
    });

    // 保存成功后刷新当前页数据
    row.isEditor = false;
    editingRow.value = null;
    ElMessage.success('保存成功');
    loadTableData(); // 刷新当前页，确保数据最新
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败');
  }
};

// 删除行
const delRow = async (row: any, index: number) => {
  try {
    await ElMessageBox.confirm(
        '确定要删除该配置吗?',
        '警告',
        { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
    );

    // 调用删除接口
    if (row) {
      await DelPddIllegalDeductionSet(row).then(res => {
        if(res.success){
          ElMessage.success('删除成功');
        }
      });
    }

    // 删除后刷新当前页（如果当前页数据为空，自动跳至上一页）
    const currentPageData = tableData.value;
    currentPageData.splice(index, 1);
    // 如果删除后当前页无数据且不是第一页，则返回上一页
    if (currentPageData.length === 0 && pagination.currentPage > 1) {
      pagination.currentPage--;
    }
    loadTableData(); // 重新加载数据
  } catch (error) {
    // 用户取消删除时不处理
  }
};
</script>

<style scoped>
.tab {
  height: 500px;
  display: flex;
  flex-direction: column;
}

.form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.table-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.btn {
  margin-bottom: 10px;
}

.pagination-container {
  position: sticky;
  bottom: 0;
  background: white;
  z-index: 10;
  padding: 12px 0;
  border-top: 1px solid #ebeef5;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

/* 文本省略样式 */
.text-ellipsis {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  padding: 0 10px;
}

/* 表格行高固定 */
.el-table__row {
  height: 40px !important; /* 固定行高 */
}

/* 输入框高度适应行高 */
.el-input {
  height: 32px !important;
}

.el-input__inner {
  height: 100% !important;
  line-height: 30px !important;
}
</style>
