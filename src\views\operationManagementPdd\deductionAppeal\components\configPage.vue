<template>
  <div class="tab">
    <el-form :model="formModel" ref="tableForm">
      <div class="btn">
        <el-button type='primary' link @click='addRow'>新增行</el-button>
      </div>
      <el-table
          :data="tableData"
          stripe
          border
          header-cell-class-name='x-table-header'
          cell-class-name='x-table-cell'
          row-key="id"
      >
        <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>

        <el-table-column prop="dutyType" label="类型" align="center">
          <template #default="{row, $index}">
            <el-form-item
                v-if="row.isEditor"
                :prop="`tableData.${$index}.dutyType`"
                :rules="rules.dutyType"
            >
              <el-input v-model="row.dutyType" />
            </el-form-item>
            <span v-else>{{ row.dutyType }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="dutyDept" label="部门" align="center">
          <template #default="{row, $index}">
            <el-form-item
                v-if="row.isEditor"
                :prop="`tableData.${$index}.dutyDept`"
                :rules="rules.dutyDept"
            >
              <el-input v-model="row.dutyDept"/>
            </el-form-item>
            <span v-else>{{ row.dutyDept }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="keyWord" label="关键字" align="center">
          <template #default="{row, $index}">
            <el-form-item
                v-if="row.isEditor"
                :prop="`tableData.${$index}.keyWord`"
                :rules="rules.keyWord"
            >
              <el-input v-model="row.keyWord"/>
            </el-form-item>
            <span v-else>{{ row.keyWord }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="180">
          <template #default="{row, $index}">
            <div class="action-buttons">
              <el-button
                  type='primary'
                  text
                  :disabled="!!editingRow && editingRow.id !== row.id"
                  @click="editRow(row, $index)"
              >
                {{ row.isEditor ? '取消' : '编辑' }}
              </el-button>

              <el-button
                  type="primary"
                  text
                  v-if="row.isEditor"
                  @click="saveRow(row, $index)"
              >
                保存
              </el-button>

              <el-button
                  type="danger"
                  text
                  :disabled="row.isEditor"
                  @click="delRow(row.id, $index)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页控件 -->
      <div class="pagination" v-if="total > 0">
        <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import {
  DelPddIllegalDeductionSet,
  GetPddIllegalDeductionSetPageList,
  SavePddIllegalDeductionSet
} from "/@/api/operatemanage/pddDeductionAppeal";

// 表单模型和校验规则
const formModel = reactive({ tableData: [] as any[] });
const tableForm = ref<FormInstance>();
const rules = reactive<FormRules>({
  dutyType: [{ required: true, message: '请输入类型', trigger: 'blur' }],
  dutyDept: [{ required: true, message: '请输入部门', trigger: 'blur' }],
  keyWord: [{ required: true, message: '请输入关键字', trigger: 'blur' }],
});

// 表格数据和编辑状态
const tableData = ref<any[]>([]);
const editingRow = ref<any>(null); // 当前正在编辑的行
const total = ref(0); // 总条数

// 分页参数（CurrentPage默认1，PageSize设为10）
const pagination = reactive({
  currentPage: 1,
  pageSize: 10 // 每页10条
});

// 初始化加载数据
onMounted(() => {
  loadTableData();
});

// 加载表格数据（带分页）
const loadTableData = async () => {
  try {
    // 传递分页参数给后端
    const response = await GetPddIllegalDeductionSetPageList({
      currentPage: pagination.currentPage,
      pageSize: pagination.pageSize
    });
    // 解析接口返回的列表和总条数
    const { list, total: totalCount } = response.data || {};
    tableData.value = (list || []).map(item => ({
      ...item,
      isEditor: false
    }));
    formModel.tableData = [...tableData.value];
    total.value = totalCount || 0; // 更新总条数
  } catch (error) {
    console.error('数据加载失败:', error);
    ElMessage.error('数据加载失败');
  }
};

// 分页大小改变时触发
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.currentPage = 1; // 重置为第一页
  loadTableData(); // 重新加载数据
};

// 页码改变时触发
const handleCurrentChange = (page: number) => {
  pagination.currentPage = page;
  loadTableData(); // 重新加载数据
};

// 新增行
const addRow = () => {
  if (editingRow.value) {
    ElMessage.warning('请先完成当前编辑');
    return;
  }

  const newRow = {
    id: Date.now(), // 临时ID
    dutyType: "",
    dutyDept: "",
    keyWord: "",
    isEditor: true
  };

  tableData.value.push(newRow);
  formModel.tableData = [...tableData.value];
  editingRow.value = newRow;
};

// 编辑行
const editRow = (row: any, index: number) => {
  if (editingRow.value) {
    // 如果是取消当前行的编辑
    if (editingRow.value.id === row.id) {
      // 如果是新增行取消编辑，则直接删除
      if (typeof row.id === 'number' && row.id > 100000) {
        tableData.value.splice(index, 1);
      }
      row.isEditor = false;
      editingRow.value = null;
      return;
    }
    ElMessage.warning('请先完成当前编辑');
    return;
  }

  row.isEditor = true;
  editingRow.value = row;
};

// 保存行
const saveRow = async (row: any, index: number) => {
  if (!tableForm.value) return;

  try {
    // 验证当前行
    await tableForm.value.validateField(`tableData.${index}`);

    const { id, isEditor, ...payload } = row;
    const res = await SavePddIllegalDeductionSet({
      ...payload,
      type: 0,
      id: typeof id === 'number' && id > 100000 ? undefined : id // 移除临时ID
    });

    // 保存成功后刷新当前页数据
    row.isEditor = false;
    editingRow.value = null;
    ElMessage.success('保存成功');
    loadTableData(); // 刷新当前页，确保数据最新
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败');
  }
};

// 删除行
const delRow = async (id: any, index: number) => {
  try {
    await ElMessageBox.confirm(
        '确定要删除该配置吗?',
        '警告',
        { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
    );

    // 调用删除接口
    if (id && typeof id !== 'number') {
      await DelPddIllegalDeductionSet(id);
    }

    // 删除后刷新当前页（如果当前页数据为空，自动跳至上一页）
    const currentPageData = tableData.value;
    currentPageData.splice(index, 1);
    // 如果删除后当前页无数据且不是第一页，则返回上一页
    if (currentPageData.length === 0 && pagination.currentPage > 1) {
      pagination.currentPage--;
    }
    loadTableData(); // 重新加载数据
    ElMessage.success('删除成功');
  } catch (error) {
    // 用户取消删除时不处理
  }
};
</script>

<style scoped>
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.x-table-header {
  background-color: #f5f7fa;
  font-weight: bold;
}

/* 分页样式 */
.pagination {
  margin-top: 16px;
  text-align: right;
  padding-right: 10px;
}
</style>
