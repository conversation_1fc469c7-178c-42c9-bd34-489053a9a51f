<template>
	<Container v-loading="loading">
		<template #header>
			<div class="topCss">
				<div class="publicCss" style="width: 200px">
					<manyInput v-model:inputt="query.styleCode" title="款式编码" :verifyNumber="false" placeholder="款式编码" :maxRows="100" :maxlength="3000" />
				</div>
				<div class="stock-query-container">
					<el-select v-model="query.salesVolumeType" clearable filterable placeholder="款式编码销量类型" class="query-type-select" @change="taxTypeChange($event, 1)">
						<el-option value="大于" label="大于" />
						<el-option value="等于" label="等于" />
						<el-option value="小于" label="小于" />
						<el-option value="介于" label="介于" />
					</el-select>
					<el-input-number
						:controls="false"
						:min="0"
						:max="999999"
						:precision="2"
						:placeholder="query.salesVolumeType == '介于' ? '款式编码销量最小值' : '款式编码销量'"
						class="stock-input"
						v-model="query.salesVolumeMin"
					/>
					<el-input-number
						:controls="false"
						:min="0"
						:max="999999"
						:precision="2"
						v-show="query.salesVolumeType == '介于'"
						placeholder="款式编码销量最大值"
						class="stock-input"
						v-model="query.salesVolumeMax"
					/>
				</div>
				<el-select v-model="query.is1688" placeholder="款式编码是否1688选品中心" class="publicCss" style="width: 181px" filterable clearable>
					<el-option label="是" :value="1" />
					<el-option label="否" :value="0" />
				</el-select>
				<div class="publicCss" style="width: 200px">
					<manyInput v-model:inputt="query.goodsCode" title="商品编码" :verifyNumber="false" placeholder="商品编码" :maxRows="100" :maxlength="3000" />
				</div>
				<dataRange
					v-model:startDate="query.startLatestStorageTime"
					v-model:endDate="query.endLatestStorageTime"
					class="publicCss"
					style="width: 270px"
					start-placeholder="最新入库开始时间"
					end-placeholder="最新入库结束时间"
				/>
				<el-select v-model="query.isListAndSale" placeholder="是否上架售卖" class="publicCss" filterable clearable>
					<el-option label="是" :value="1" />
					<el-option label="否" :value="0" />
				</el-select>
				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
					<el-button type="primary" @click="exportProps(1)">导出</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="productManagement202507211158" :tableCols="tableCols" :query="query" :query-api="GetGoodsAssociationAlibaba" showsummary />
			<el-dialog v-model="detailInfo.dialogVisible" title="详情" width="60%" draggable append-to-body style="margin-top: -7vh !important">
				<Container v-loading="detailsLoading">
					<template #header>
						<div class="topCss">
							<dataRange
								v-model:startDate="detailInfo.rowParameter.startDate"
								v-model:endDate="detailInfo.rowParameter.endDate"
								class="publicCss"
								style="width: 200px"
								start-placeholder="开始时间"
								end-placeholder="结束时间"
							/>
							<el-select
								v-model="detailInfo.rowParameter.shopCode"
								placeholder="店铺名称"
								style="width: 180px"
								class="publicCss"
								clearable
								filterable
								multiple
								collapse-tags
								collapse-tags-tooltip
								:reserve-keyword="false"
							>
								<el-option v-for="item in shopList" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
							<div class="stock-query-container">
								<el-select v-model="detailInfo.rowParameter.proSalesVolumeType" clearable filterable placeholder="销量类型" class="query-type-select" @change="taxTypeChange($event, 2)">
									<el-option value="大于" label="大于" />
									<el-option value="等于" label="等于" />
									<el-option value="小于" label="小于" />
									<el-option value="介于" label="介于" />
								</el-select>
								<el-input-number
									:controls="false"
									:min="0"
									:max="999999"
									:precision="2"
									:placeholder="detailInfo.rowParameter.proSalesVolumeType == '介于' ? '销量最小值' : '销量'"
									class="stock-input"
									v-model="detailInfo.rowParameter.proSalesVolumeMin"
								/>
								<el-input-number
									:controls="false"
									:min="0"
									:max="999999"
									:precision="2"
									v-show="detailInfo.rowParameter.proSalesVolumeType == '介于'"
									placeholder="销量最大值"
									class="stock-input"
									v-model="detailInfo.rowParameter.proSalesVolumeMax"
								/>
							</div>
							<div class="publicCss" style="width: 170px">
								<manyInput v-model:inputt="detailInfo.rowParameter.proCodeString" title="产品ID" :verifyNumber="false" placeholder="产品ID" :maxRows="100" :maxlength="3000" />
							</div>
							<div class="pb5">
								<el-button type="primary" @click="onGetList">查询</el-button>
								<el-button type="primary" @click="exportProps(2)">导出</el-button>
							</div>
						</div>
					</template>
					<template #content>
						<vxetable
							v-if="detailInfo.dialogVisible"
							ref="table1"
							id="productManagementDetails202507211425"
							height="400"
							:tableCols="tableCols1"
							:query="detailInfo.rowParameter"
							:query-api="GetGoodsAssociationAlibabaDetails"
							showsummary
						/>
					</template>
				</Container>
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted } from 'vue';
import { GetGoodsAssociationAlibaba, ExportGoodsAssociationAlibaba, GetGoodsAssociationAlibabaDetails, ExportGoodsAssociationAlibabaDetails } from '/@/api/bookkeeper/dayReportV2';
import { GetShopList } from '/@/api/operatemanage/shop';
import dayjs from 'dayjs';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const manyInput = defineAsyncComponent(() => import('/@/components/yhCom/manyInput.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const startDate = dayjs().subtract(1, 'month').format('YYYY-MM-DD');
const endDate = dayjs().format('YYYY-MM-DD');
const query = ref({
	styleCode: '', //款式编码
	goodsCode: '', //商品编码
	startLatestStorageTime: startDate, //最新入库开始时间
	endLatestStorageTime: endDate, //最新入库结束时间
	is1688: null, //是否1688
	isListAndSale: null, //是否上架售卖
	salesVolumeType: null, //款式编码销量类型
	salesVolumeMin: undefined, //款式编码销量最小值
	salesVolumeMax: undefined, //款式编码销量最大值
});
const detailInfo = ref({
	dialogVisible: false,
	rowParameter: {
		goodsCode: null, //商品编码
		startDate: startDate, //开始时间
		endDate: endDate, //结束时间
		proSalesVolumeType: null, //商品ID销量类型
		proSalesVolumeMin: undefined, //商品ID销量最小值
		proSalesVolumeMax: undefined, //商品ID销量最大值
		shopCode: [] as string[], //店铺
		proCodeString: '',
		proCode: [] as string[], //产品编码数组
	},
});
const detailsLoading = ref(false);
const table = ref();
const table1 = ref();
const loading = ref(false);
const shopList = ref<any[]>([]);

const onGetList = () => {
	detailInfo.value.rowParameter.proCode = detailInfo.value.rowParameter.proCodeString ? detailInfo.value.rowParameter.proCodeString.split(',') : [];
	table1.value.refreshTable(true);
};

const exportProps = async (val: number) => {
	const isDetail = val === 2;
	loading.value = !isDetail;
	detailsLoading.value = isDetail;
	const api = isDetail ? ExportGoodsAssociationAlibabaDetails : ExportGoodsAssociationAlibaba;
	const params = isDetail ? { ...detailInfo.value.rowParameter, ...table1.value.query } : { ...query.value, ...table.value.query };
	try {
		const data = await api(params);
		if (data.success) {
			window.$message.success(data.msg || '导出成功,稍后请到下载管理查看');
		} else {
			window.$message.error(data.msg || '导出失败');
		}
	} catch (error) {
		window.$message.error('导出异常，请稍后重试');
	} finally {
		loading.value = false;
		detailsLoading.value = false;
	}
};

const taxTypeChange = (e: any, type: number) => {
	if (type == 1) {
		query.value.salesVolumeMax = undefined;
		query.value.salesVolumeMin = undefined;
	} else if (type == 2) {
		detailInfo.value.rowParameter.proSalesVolumeMax = undefined;
		detailInfo.value.rowParameter.proSalesVolumeMin = undefined;
	}
};

const openDetails = async (row: any) => {
	detailInfo.value.dialogVisible = true;
	detailInfo.value.rowParameter.startDate = startDate;
	detailInfo.value.rowParameter.endDate = endDate;
	detailInfo.value.rowParameter.goodsCode = row.goodsCode;
};

const tableCols1 = ref<VxeTable.Columns[]>([
	{ width: 'auto', sortable: true, field: 'orderYearMonthDay', title: '日期', align: 'center', formatter: (row: any) => dayjs(row.orderYearMonthDay.toString(), 'YYYYMMDD').format('YYYY-MM-DD') },
	{ width: '300', sortable: true, field: 'shopName', title: '店铺名称', align: 'center' },
	{ width: 'auto', sortable: true, field: 'proCode', title: '产品ID', align: 'center' },
	{ width: 'auto', sortable: true, field: 'salesValue', title: '销量', align: 'right' },
]);
const tableCols = ref<VxeTable.Columns[]>([
	{ width: '220', sortable: true, field: 'styleCode', title: '款式编码', align: 'center' },
	{ width: '120', sortable: true, field: 'styleCodeSalesVolume', title: '款式编码销量', align: 'right' },
	{ width: '180', sortable: true, field: 'is1688', title: '款式编码是否1688选品中心', align: 'center', formatter: (row: any) => (row.is1688 == 1 ? '是' : row.is1688 == 0 ? '否' : '') },
	{ width: '160', sortable: true, field: 'goodsCode', title: '商品编码', align: 'center' },
	{ width: 'auto', sortable: true, field: 'goodsName', title: '商品名称', align: 'center' },
	{ width: '100', sortable: true, field: 'actualInventoryQuantity', title: '实际库存数', align: 'right' },
	{ width: '135', sortable: true, field: 'latestStorageTime', title: '最新入库产品时间', align: 'center' },
	{ width: '130', sortable: true, field: 'shopNumber', title: '阿里巴巴店铺数量', align: 'right', type: 'click', handle: (row: any) => openDetails(row) },
	{
		width: '120',
		sortable: true,
		field: 'proCodeNumber',
		title: '商品ID数量',
		align: 'right',
		type: 'click',
		handle: (row: any) => openDetails(row),
	},
	{ width: '110', sortable: true, field: 'isListAndSale', title: '是否上架售卖', align: 'center', formatter: (row: any) => (row.isListAndSale == 1 ? '是' : row.isListAndSale == 0 ? '否' : '') },
]);

onMounted(async () => {
	const { data, success } = await GetShopList({ platform: 4, currentPage: 1, PageSize: 100000 });
	if (success) {
		shopList.value = data.list.map((item: any) => {
			return {
				value: item.shopCode,
				label: item.shopName,
			};
		});
	}
});
</script>

<style scoped lang="scss">
.stock-query-container {
	display: flex;
	align-items: baseline;
	margin: 0 10px 5px 0;

	.query-type-select {
		width: 130px;

		:deep(.el-input__inner) {
			border: none;
			border-radius: 0;
			border-right: 1px solid #dcdfe6;
			height: 24px;
		}
	}

	.stock-input {
		width: 110px;

		:deep(.el-input__inner) {
			border: none;
			border-radius: 0;
			height: 24px;
		}
	}
}

::v-deep .el-select__tags-text {
	max-width: 65px;
}
</style>
