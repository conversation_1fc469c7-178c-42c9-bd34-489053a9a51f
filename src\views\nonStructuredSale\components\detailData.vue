<template>
	<Container v-loading="loading">
		<template #header>
			<div class="topCss">
				<dataRange v-model:startDate="query.startDate" v-model:endDate="query.endDate" class="publicCss" style="width: 220px" start-placeholder="支付开始时间" end-placeholder="支付结束时间" />
				<el-select class="publicCss" v-model="query.architecture" placeholder="所属架构" clearable filterable multiple collapse-tags collapse-tags-tooltip>
					<el-option v-for="item in buildList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<div class="publicCss" style="width: 140px">
					<manyInput v-model:inputt="query.goodsCode" title="商品编码" :verifyNumber="false" placeholder="商品编码" :maxRows="100" :maxlength="3000" />
				</div>
				<el-input v-model.trim="query.proCode" placeholder="产品ID" clearable maxlength="50" class="publicCss" />
				<el-select class="publicCss" v-model="query.platForm" placeholder="售卖平台" clearable filterable multiple collapse-tags collapse-tags-tooltip>
					<el-option v-for="item in salesPlatform" :key="item.value" :label="item.label" :value="item.value" />
					<!-- <el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" /> -->
				</el-select>
				<el-select
					v-model="query.shopCodeList"
					placeholder="售卖店铺"
					clearable
					filterable
					multiple
					collapse-tags
					collapse-tags-tooltip
					:reserve-keyword="false"
					class="public_Css"
					style="width: 220px"
					:filter-method="filterShops"
				>
					<el-option v-for="item in filteredShopList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.groupIdList" placeholder="售卖运营组" clearable filterable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false" class="public_Css">
					<el-option v-for="item in props.groupList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.operateSpecialUserIdList" placeholder="售卖运营专员" class="public_Css" filterable clearable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false">
					<el-option v-for="item in props.directorlist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.userIdList" placeholder="售卖运营助理" class="public_Css" filterable clearable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false">
					<el-option v-for="item in props.directorlist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<div class="publicCss" style="width: 140px">
					<manyInput v-model:inputt="query.orderNos" title="线上单号" :verifyNumber="false" placeholder="线上单号" :maxRows="100" :maxlength="3000" />
				</div>
				<div class="publicCss" style="width: 140px">
					<manyInput v-model:inputt="query.orderNoInners" title="内部订单号" :verifyNumber="false" placeholder="内部订单号" :maxRows="100" :maxlength="3000" />
				</div>
				<div class="pb5">
					<el-button type="primary" @click="getList">查询</el-button>
					<el-button type="primary" @click="exportProps">导出</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="nonStructuredSaleDetailData202507241137" :tableCols="tableCols" :query="query" :query-api="GetNoJiaGouShouMaiGoods" showsummary />
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, PropType, onMounted, computed, watch } from 'vue';
import { GetNoJiaGouShouMaiGoods, ExportNoJiaGouShouMaiGoods } from '/@/api/bookkeeper/selectionProductCenterSaleDataPlanA';
const manyInput = defineAsyncComponent(() => import('/@/components/yhCom/manyInput.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
import { platformlist } from '/@/utils/tools';
import dayjs from 'dayjs';
interface OptionItem {
	value: string | number;
	label: string;
	platform?: number; // 店铺的平台字段，可选
}
const props = defineProps({
	directorlist: {
		type: Array as PropType<OptionItem[]>,
		default: () => [],
	},
	shopList: {
		type: Array as PropType<OptionItem[]>,
		default: () => [],
	},
	groupList: {
		type: Array as PropType<OptionItem[]>,
		default: () => [],
	},
});
const query = ref({
	goodsCode: '', //商品编码
	proCode: '', //产品ID
	startDate: dayjs().format('YYYY-MM-DD'),
	endDate: dayjs().format('YYYY-MM-DD'),
	architecture: [],
	platForm: [],
	shopCodeList: [],
	groupIdList: [],
	operateSpecialUserIdList: [],
	userIdList: [],
	orderNos: '',
	orderNoInners: '',
	isSale: 1,
});
const table = ref();
const loading = ref(false);
const buildList = ref<OptionItem[]>([
	{ value: '2', label: '汪大侠' },
	{ value: '4', label: '徐琛' },
	{ value: '11', label: '左玉玲' },
]);
const salesPlatform = ref<OptionItem[]>([
	{ value: 1, label: '天猫' },
	{ value: 4, label: '阿里巴巴' },
	{ value: 8, label: '淘工厂' },
	{ value: 9, label: '淘宝' },
	{ value: 10, label: '苏宁' },
	{ value: 2, label: '拼多多' },
	{ value: 6, label: '抖音' },
]);

// 店铺过滤相关
const filteredShopList = ref<OptionItem[]>([]);
const shopFilterKeyword = ref('');

// 获取有效的平台值
const getValidPlatforms = () => {
	return salesPlatform.value.map((item) => item.value);
};

// 根据平台过滤店铺列表
const getFilteredShopsByPlatform = () => {
	const validPlatforms = getValidPlatforms();
	return props.shopList.filter((shop) => shop.platform && validPlatforms.includes(shop.platform));
};

// 店铺过滤方法
const filterShops = (keyword: string) => {
	shopFilterKeyword.value = keyword;

	// 首先根据平台过滤
	const platformFilteredShops = getFilteredShopsByPlatform();

	if (!keyword) {
		filteredShopList.value = platformFilteredShops;
		return;
	}

	// 优先精确匹配
	const exactMatch = platformFilteredShops.filter((item) => item.label === keyword);
	if (exactMatch.length > 0) {
		filteredShopList.value = exactMatch;
		return;
	}

	// 如果没有精确匹配，则进行模糊匹配
	filteredShopList.value = platformFilteredShops.filter((item) => item.label.toLowerCase().includes(keyword.toLowerCase()));
};
const exportProps = async () => {
	loading.value = true;
	await ExportNoJiaGouShouMaiGoods({ ...query.value, ...table.value.query })
		.then((data: any) => {
			loading.value = false;
			const aLink = document.createElement('a');
			let blob = new Blob([data], { type: 'application/vnd.ms-excel' });
			aLink.href = URL.createObjectURL(blob);
			aLink.setAttribute('download', '非架构售卖明细-导出' + new Date().toLocaleString() + '.xlsx');
			aLink.click();
		})
		.catch(() => {
			loading.value = false;
		});
};

const getList = () => {
	table.value.refreshTable(true);
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, width: 'auto', align: 'center', field: 'yearMonthDayDate', title: '日期', formatter: 'formatDate' },
	{ width: 'auto', sortable: true, field: 'goodsCode', title: '商品编码', align: 'center' },
	{
		width: 'auto',
		sortable: true,
		field: 'architecture',
		title: '所属架构',
		align: 'center',
		formatter: (row: any) => buildList.value.find((item) => item.value == row.architecture)?.label,
	},
	{ width: 'auto', sortable: true, field: 'proCode', title: '产品ID', align: 'center' },
	{ width: 'auto', sortable: true, field: 'platForm', title: '售卖平台', align: 'center', formatter: 'formatPlatform' },
	{ width: 'auto', sortable: true, field: 'shopName', title: '售卖店铺', align: 'center' },
	{ width: 'auto', sortable: true, field: 'groupName', title: '售卖运营组', align: 'center' },
	{ width: 'auto', sortable: true, field: 'operateSpecialUserName', title: '售卖运营专员', align: 'center' },
	{ width: 'auto', sortable: true, field: 'userName', title: '售卖运营助理', align: 'center' },
	{ width: 'auto', sortable: true, field: 'orderNo', title: '线上订单号', align: 'center' },
	{ width: 'auto', sortable: true, field: 'orderNoInner', title: '内部订单号', align: 'center' },
	{ width: 'auto', sortable: true, field: 'yearMonthDayDate', title: '付款日期', align: 'center', formatter: 'formatDate' },
	// { width: 'auto', sortable: true, field: 'payTime', title: '付款日期', align: 'center' },
]);

onMounted(async () => {
	const a = ['汪大侠', '左玉玲', '徐琛'];
	// 初始化店铺列表，应用平台过滤
	filteredShopList.value = getFilteredShopsByPlatform();
	console.log(props.shopList, 'props.shopList');
});

// 监听 shopList 变化
watch(
	() => props.shopList,
	() => {
		if (!shopFilterKeyword.value) {
			filteredShopList.value = getFilteredShopsByPlatform();
		} else {
			filterShops(shopFilterKeyword.value);
		}
	},
	{ immediate: true }
);

// 监听 salesPlatform 变化
watch(
	() => salesPlatform.value,
	() => {
		if (!shopFilterKeyword.value) {
			filteredShopList.value = getFilteredShopsByPlatform();
		} else {
			filterShops(shopFilterKeyword.value);
		}
	},
	{ immediate: true }
);
</script>

<style scoped lang="scss">
.public_Css {
	width: 180px;
	margin: 0 5px 5px 0;
}

::v-deep .el-select__tags-text {
	max-width: 50px;
}
</style>
