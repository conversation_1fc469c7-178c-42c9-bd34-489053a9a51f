<template>
	<div style="height: 100%; width: 100%; margin: 0; display: block">
		<div v-if="props.isNeedTools">
			<vxe-toolbar custom ref="toolbarRef" :refresh="{ queryMethod: props.isNeedQueryApi ? getList : () => {} }">
				<template #buttons>
					<el-button @click="exportProps" type="primary" v-if="exportApi" :disabled="isExport">导出</el-button>
					<slot name="toolbar_buttons"></slot>
				</template>
				<template #tools>
					<slot name="tools_right"></slot>
					<div style="padding-right: 15px" v-if="showExpendBtn">
						<vxe-button circle icon="vxe-icon-add-sub" title="树展开收起" @click="handleClickExpend()"></vxe-button>
					</div>
				</template>
			</vxe-toolbar>
		</div>
		<div :style="tableHeight">
			<!-- 虚拟滚动表格,支持普通表格,树形表格 -->
			<vxe-table
				class="vxetableCss"
				:id="props.id"
				:tree-config="props.treeConfig"
				:row-config="{ keyField: props.keyField, isHover: true, isCurrent: true }"
				:column-config="props.columnConfig"
				:edit-config="props.editConfig"
				:custom-config="{ ...props.customConfig, restoreStore: restoreStore, updateStore: updateStore }"
				:border="props.border"
				ref="tableRef"
				:height="props.height ? props.height : '100%'"
				:show-overflow="props.showOverflow"
				:show-header-overflow="props.showHeaderOverflow"
				:show-footer-overflow="props.showFooterOverflow"
				:print-config="{}"
				:loading="loading"
				:data="tableData"
				:sort-config="{ remote: remote }"
				@sort-change="sortChange"
				@cell-click="cellClick"
				:checkbox-config="{ ...checkboxConfig, visibleMethod: visibleMethod, range: props.treeConfig ? false : true }"
				@checkbox-change="checkboxChange"
				@checkboxAll="checkboxAll"
				@checkboxRangeEnd="checkboxRangeEnd"
				:scroll-y="props.scrollY"
				:scroll-x="props.scrollX"
				:show-footer="showsummary"
				:footer-data="footerData"
				:mergeCells="mergeCells"
				@custom="tooclick"
				@footer-cell-click="footerCellClick"
			>
				<vxe-column type="seq" :width="Array.isArray(footerData) ? 50 : 50" v-if="isNeedIndex" :fixed="props.isIndexFixed ? 'left' : ''" align="center" title="#">
					<template #footer="{ row }" v-if="Array.isArray(footerData)">
						{{ row.index }}
					</template>
					<template #footer="{ row }" v-else> 合计 </template>
				</vxe-column>
				<vxe-column type="checkbox" width="40" v-if="isNeedCheckBox" :fixed="props.isCheckBoxFixed ? 'left' : ''" align="center" />
				<vxe-column type="expand" width="45" v-if="isNeedExpand" :fixed="props.isCheckBoxFixed ? 'left' : ''" align="center">
					<template #content="{ row }">
						<slot :row="row" name="expandCols"></slot>
					</template>
				</vxe-column>
				<slot name="leftCols"></slot>
				<template v-for="(item, i) in props.tableCols" :key="item.field">
					<!-- 普通列 -->
					<vxe-column
						v-if="!item.children || item.children.length === 0"
						:tree-node="!!item.treeNode ? true : false"
						:field="item.field"
						:title="item.title"
						:align="item.align"
						:sortable="item.sortable"
						:width="item.width == 'auto' || item.width === undefined ? '' : item.width"
						:visible="item.visible === undefined || item.visible === true ? true : false"
						:fixed="item.fixed"
						:title-prefix="item.tipmesg ? { content: item.tipmesg } : undefined"
						:min-width="item.minWidth ? item.minWidth : ''"
						v-auth="item.permissions"
					>
						<template #default="{ row, rowIndex }" v-if="!item.type">
							<slot :name="item.field" v-bind:row="row" v-bind:index="rowIndex">
								<template v-if="item.component">
									<component :is="item.component" :row="row" :index="rowIndex" :value="row[item.field!]" />
								</template>
								<span v-else-if="!item.formatter" :style="{ color: typeof item.color === 'function' ? item.color(row) : item.color }">{{ row[item.field!] }}</span>
								<span v-else-if="typeof item.formatter === 'string' && item.formatter === 'formatLinkProcode'" v-html="formats(row, item)"></span>
								<span v-else-if="typeof item.formatter === 'string'" :style="{ color: typeof item.color === 'function' ? item.color(row) : item.color }">{{ formats(row, item) }}</span>
								<span v-else-if="typeof item.formatter === 'function'" :style="{ color: typeof item.color === 'function' ? item.color(row) : item.color }">{{ item.formatter(row) }}</span>
							</slot>
						</template>
						<template #default="{ row, rowIndex }" v-if="item.type">
							<vxetableColumn :data="tableData" :tableCols="props.tableCols" :item="item" :rowIndex="rowIndex" :row="row" :type="item.type" />
						</template>
						<template #footer="{ row }">
							<span :style="item.summaryEvent ? 'cursor: pointer;color: red;' : ''">{{ row[item.field!] }}</span>
						</template>
					</vxe-column>
					<!-- 表头分组 -->
					<vxe-colgroup
						v-else-if="item.children && Array.isArray(item.children) && item.children.length > 0"
						:tree-node="!!item.treeNode ? true : false"
						:field="item.field"
						:title="item.title"
						:align="item.align"
						:sortable="item.sortable"
						:width="item.width == 'auto' || item.width === undefined ? '' : item.width"
						:visible="item.visible === undefined || item.visible === true ? true : false"
						:fixed="item.fixed"
						:title-prefix="item.tipmesg ? { content: item.tipmesg } : undefined"
						:min-width="item.minWidth ? item.minWidth : ''"
						v-auth="item.permissions"
					>
						<vxe-column
							v-for="(child, j) in item.children"
							:tree-node="!!child.treeNode ? true : false"
							:field="child.field"
							:title="child.title"
							:align="child.align"
							:sortable="child.sortable"
							:width="child.width == 'auto' || child.width === undefined ? '' : child.width"
							:visible="child.visible === undefined || child.visible === true ? true : false"
							:fixed="child.fixed"
							:title-prefix="child.tipmesg ? { content: child.tipmesg } : undefined"
							:min-width="child.minWidth ? child.minWidth : ''"
							v-auth="child.permissions"
						>
							<template #default="{ row, rowIndex }" v-if="!child.type">
								<slot :name="child.field" v-bind:row="row" v-bind:index="rowIndex">
									<template v-if="child.component">
										<component :is="child.component" :row="row" :index="rowIndex" :value="row[child.field!]" />
									</template>
									<span v-else-if="!child.formatter" :style="{ color: typeof child.color === 'function' ? child.color(row) : child.color }">{{ row[child.field!] }}</span>
									<span v-else-if="typeof child.formatter === 'string' && child.formatter === 'formatLinkProcode'" v-html="formats(row, child)"></span>
									<span v-else-if="typeof child.formatter === 'string'" :style="{ color: typeof child.color === 'function' ? child.color(row) : child.color }">{{ formats(row, child) }}</span>
									<span v-else-if="typeof child.formatter === 'function'" :style="{ color: typeof child.color === 'function' ? child.color(row) : child.color }">{{ child.formatter(row) }}</span>
								</slot>
							</template>
							<template #default="{ row, rowIndex }" v-if="child.type">
								<vxetableColumn :data="tableData" :tableCols="props.tableCols" :item="child" :rowIndex="rowIndex" :row="row" :type="child.type" />
							</template>
							<template #footer="{ row }">
								<span :style="child.summaryEvent ? 'cursor: pointer;color: red;' : ''">{{ row[child.field!] }}</span>
							</template>
						</vxe-column>
					</vxe-colgroup>
				</template>
				<slot name="rightCols"></slot>
			</vxe-table>
		</div>
		<div>
			<!-- 分页  -->
			<vxe-pager
				v-if="isNeedPager"
				:current-page="query.currentPage"
				:page-size="query.pageSize"
				:page-sizes="props.pageSizes"
				:total="listTotal"
				:auto-hidden="props.pagerAutoHidden"
				:layouts="['Home', 'PrevPage', 'Jump', 'NextPage', 'End', 'Sizes', 'PageCount', 'Total']"
				@page-change="pageChange"
			/>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, nextTick, onMounted, defineExpose, computed, PropType } from 'vue';
import type { VxeTableInstance, VxeToolbarInstance } from 'vxe-table';
import { ElMessage } from 'element-plus';
import vxetableColumn from './vxetableColumn.vue';
import { formatters } from '/@/utils/vxetableFormats';
import { GetVxeTableColumnCacheAsync, SetVxeTableColumnCacheAsync } from '/@/api/admin/business';
import request from '/@/utils/yhrequest';
/*----------------------------------------------- 传值部分 ------------------------------------------*/
const props = defineProps({
	getCols: { type: Function, default: () => null }, //获取列头方法
	method: { type: String, default: 'POST' }, //请求方式
	height: { type: String, default: '' }, //表格高度
	query: { type: Object, default: () => {} }, //查询条件
	id: { type: String, default: '' }, //表格id
	data: { type: Array, default: () => [] }, //表格数据,不需要掉接口,就传入数据
	isNeedQueryApi: { type: Boolean, default: true }, //是否需要调用接口
	isNeedDisposeProps: { type: Boolean, default: false }, //是否需要前端处理数据
	tableCols: { type: Array<VxeTable.Columns>, default: () => [] }, //列头
	currentPage: { type: Number, default: 1 }, //当前页
	pageSize: { type: Number, default: 50 }, //每页显示条数
	pageSizes: { type: Array<number>, default: () => [50, 100, 200, 300] }, //页码尺寸
	pagerAutoHidden: { type: Boolean, default: false }, //是否自动隐藏分页
	queryApi: { type: [Function, String], default: () => {} }, //查询接口
	// visibleMethod: { type: Function, default: () => Boolean }, //复选框是否显示的方法
	orderBy: { type: String, default: '' }, //排序字段
	isAsc: { type: Boolean, default: false }, //是否升序
	remote: { type: Boolean, default: true }, //是否服务端排序
	isIndexFixed: { type: Boolean, default: true }, //序号列是否固定
	isCheckBoxFixed: { type: Boolean, default: true }, //复选框列是否固定
	isNeedPager: { type: Boolean, default: true }, //是否需要分页
	isNeedIndex: { type: Boolean, default: true }, //是否需要序号列
	isNeedCheckBox: { type: Boolean, default: false }, //是否需要复选框列
	isNeedExpand: { type: Boolean, default: false }, //是否需要展开行
	showsummary: { type: Boolean, default: false }, //显示表尾
	showOverflow: { type: String, default: 'tooltip' }, //超出隐藏
	showHeaderOverflow: { type: String, default: 'tooltip' }, //表头内容过长时是否显示省略号
	showFooterOverflow: { type: String, default: 'tooltip' }, //表尾内容过长时是否显示省略号
	VirtualScroll: { type: Boolean, default: true }, //是否启用虚拟滚动
	border: { type: Boolean, default: true }, //边框
	isNeedTools: { type: Boolean, default: true }, //是否需要工具栏
	somerow: { type: Array, default: [] },
	keyField: { type: String, default: 'id' }, //主键字段
	mergeCells: { type: Array, default: () => [] }, //合并单元格
	exportApi: { type: Function, default: null }, //导出接口
	asyncExport: {
		type: Object as PropType<VxeTable.exportConfig>,
		default: () => {
			return {
				title: '导出数据',
				isAsync: true, // 是否异步导出
			};
		},
	},
	treeConfig: {
		type: Object,
		default: () => {
			return null;
		},
	},
	noToFixed: {
		type: Boolean,
		default: () => {
			return false;
		},
	}, //是否保留小数
	//编辑配置
	editConfig: {
		type: Object,
		default: () => {
			return {};
		},
	},
	//复选框配置
	checkboxConfig: {
		type: Object,
		default: () => {
			return {
				labelField: '', //复选框显示的字段名，可以直接显示在复选框中
				checkField: '', //绑定选中属性（行数据中必须存在该字段，否则无效）
				checkRowKeys: [], //默认选中的行数据的key值,必须要配置keyField
				// range: true, //是否启用复选框的范围选择功能
				highlight: true, //是否启用高亮选中行
				reserve: true, //是否保留选中状态
			};
		},
	},
	//自定义配置
	customConfig: {
		type: Object,
		default: () => {
			return {
				storage: {
					visible: true, //启用显示/隐藏列状态缓存
					resizable: true, //启用列宽拖动
					sort: true, //启用排序状态缓存
					fixed: true, //启用固定列状态缓存
					minWidth: '40px',
				},
			};
		},
	},
	//是否启用虚拟滚动
	scrollY: {
		type: Object,
		default: () => {
			return { enabled: true, gt: 0 };
		},
	},
	//横向虚拟滚动
	scrollX: {
		type: Object,
		default: () => {
			return { enabled: true, gt: 0 };
		},
	},
	//列配置信息
	columnConfig: {
		type: Object,
		default: () => {
			return {
				useKey: true,
				minWidth: '40px',
				isCurrent: true,
				resizable: true,
				maxFixedSize: 300,
			};
		},
	},
});
const emit = defineEmits(['cellClick', 'select', 'visibleMethod', 'disposeProps', 'summaryProps', 'sortEvent', 'footerCellClick', 'getCols']);
/*----------------------------------------------- 变量部份 ------------------------------------------*/
//固定查询条件
const query = ref({
	currentPage: props.currentPage,
	pageSize: props.pageSize,
	orderBy: props.orderBy,
	isAsc: props.isAsc,
});
const tableRef = ref<VxeTableInstance>();
const toolbarRef = ref<VxeToolbarInstance>();
const loading = ref(false);
const isExpendAll = ref(false);
const tableData = ref<any>([]);
const listTotal = ref(0);
const footerData = ref<any>([]);
const isExport = ref<boolean>(false);
/*----------------------------------------------- table功能部份 ------------------------------------------ */
const restoreStore = async ({ id, type, storeData }: any) => {
	let resp = await GetVxeTableColumnCacheAsync({ tableId: id });
	let store = null;
	if (resp && resp.success && resp.data) {
		store = JSON.parse(resp.data);
	}
	return store ?? storeData;
};

const updateStore = async ({ id, type, storeData }: any) => {
	await SetVxeTableColumnCacheAsync({ tableId: id, ColumnConfig: JSON.stringify(storeData) });
};

//滚动到表格指定行
const onScrollToRow = async (row: any) => {
	if (tableRef.value) {
		await tableRef.value.loadData(tableData.value);
		tableRef.value.scrollToRow(row, 'name').catch((err: any) => {
			console.log(`tableData[${row}]` + 'err', err);
		});
		tableRef.value.setCurrentRow(row); //高亮当前行
	}
};

const showHidenColums = (arrlist: any, visible: boolean) => {
	if (!arrlist) return;
	tableRef.value?.getTableColumn().collectColumn.forEach((column) => {
		if (column.children && column.children.length > 0) {
			column.children.forEach((child: any) => {
				if (arrlist.includes(child.field)) {
					child.visible = visible;
				}
			});
		} else if (arrlist.includes(column.field)) {
			column.visible = visible;
		}
	});
	if (tableRef.value) {
		tableRef.value?.refreshColumn();
	}
};

//展开树形表格
const expandAllTreeEvent = (verify: boolean) => {
	tableRef.value?.setAllTreeExpand(verify);
};

//清空树节点
const clearTreeExpand = () => {
	tableRef.value?.clearTreeExpand();
};

const handleClickExpend = () => {
	if (props.treeConfig?.parentField) {
		props.treeConfig.expandAll = !props.treeConfig?.expandAll;
		tableRef.value?.setAllTreeExpand(props.treeConfig.expandAll);
	} else if (props.isNeedExpand) {
		isExpendAll.value = !isExpendAll.value;
		tableRef.value?.setAllRowExpand(isExpendAll.value);
	}
};

//表格数据回显
const onAssignedData = async (data: any) => {
	tableData.value = [...(data ?? props.data ?? [])];
};

//清空复选框选中
const clearSelection = () => {
	tableRef.value?.clearCheckboxRow();
};

const matchData = (data: Public.options[], matchField: string[]) => {
	if (!data || data?.length == 0 || !matchField || matchField?.length == 0) return;
	tableData.value.forEach((item: any) => {
		data.forEach((dataItem: any) => {
			let isMatch = true;
			matchField.forEach((field) => {
				if (item[field] !== dataItem[field]) {
					isMatch = false;
				}
			});
			if (isMatch) {
				Object.keys(dataItem).forEach((key) => {
					item[key] = dataItem[key];
				});
			}
		});
	});
};

const tableHeight = computed(() => {
	let tempRowHeight = 40;
	if (tableRef && tableRef.value) {
		if (tableRef.value.size === 'medium') {
			tempRowHeight = 40;
		} else if (tableRef.value.size === 'small') {
			tempRowHeight = 36;
		} else {
			tempRowHeight = 32;
		}
	}
	return `height: calc(100% - ${props.isNeedPager ? tempRowHeight * 2 : tempRowHeight}px);`;
});
const showExpendBtn = computed(() => {
	return props.treeConfig?.parentField || props.isNeedExpand;
});

const visibleMethod = (data: any) => {
	let res = true;
	emit('visibleMethod', data, (val: any) => {
		res = val;
	});
	return res;
};

//查询数据
const getList = async () => {
	loading.value = true;
	try {
		let allData = {
			list: [],
			total: 0,
			summary: {},
			msg: '',
			success: false,
		};
		let isSuccess = false;
		if (typeof props.queryApi === 'function') {
			let { data, success } = await props.queryApi({ ...query.value, ...props.query });
			allData = data;
			isSuccess = success;
		} else {
			let { data, success } = await request({
				url: props.queryApi,
				method: props.method,
				data: { ...query.value, ...props.query },
			});
			allData = data;
			isSuccess = success;
		}
		if (isSuccess) {
			console.log(allData, 'allData');
			if (props.isNeedDisposeProps) {
				emit('disposeProps', { allData, query: query.value }, (val: any) => {
					tableData.value = val.allData.list ? val.allData.list : [];
					footerData.value = formatSummary(val.allData.summary);
					listTotal.value = val.allData.total ? val.allData.total : 0;
				});
			} else {
				tableData.value = allData.list ? allData.list : allData ? allData : [];
				footerData.value = formatSummary(allData.summary);
				listTotal.value = allData.total ? allData.total : 0;
			}
			nextTick(() => {
				if (tableRef.value && props.treeConfig?.expandAll) tableRef.value.setAllTreeExpand(true);
			});
		} else {
			ElMessage.error('获取数据失败');
		}
	} catch (error) {
		console.log(error, 'error');
		ElMessage.error('获取数据失败');
	} finally {
		loading.value = false;
	}
};
//刷新表格数据
const refreshTable = (isSearch: Boolean) => {
	if (isSearch) {
		query.value.currentPage = 1;
	}
	getList();
};

//导出数据
const exportProps = async () => {
	isExport.value = true;
	try {
		if (props.asyncExport.isAsync) {
			const { success } = await props.exportApi({ ...query.value, ...props.query });
			if (!success) return;
			window.$message.success('导出成功,稍后请到下载管理查看');
		} else {
			const res = await props.exportApi({ ...query.value, ...props.query });
			const aLink = document.createElement('a');
			let blob = new Blob([res], { type: 'application/vnd.ms-excel' });
			aLink.href = URL.createObjectURL(blob);
			console.log(blob, 'blob');
			aLink.setAttribute('download', `${props.asyncExport.title + new Date().toLocaleString()}.xlsx`);
			aLink.click();
			aLink.remove();
		}
	} catch (error) {
		console.log(error, 'error');
	} finally {
		isExport.value = false;
	}
};

//分页器事件
const pageChange = (params: any) => {
	query.value.currentPage = params.currentPage;
	query.value.pageSize = params.pageSize;
	getList();
};

//排序
const sortChange = ({ sortList }: any) => {
	const res: any = props.tableCols.find((item: VxeTable.Columns) => item.field == sortList[0]?.field) ?? '';
	let field: string = '';
	let order: string = '';
	if (res['sort-by']) {
		field = res['sort-by'] as string;
	} else {
		field = sortList[0]?.field;
	}
	order = (sortList[0]?.order as string) ?? '';
	emit('sortEvent', sortList); //排序事件
	//服务端排序
	if (props.remote) {
		query.value.orderBy = field ? field : '';
		query.value.isAsc = order === 'asc' ? true : false;
		getList();
	} else {
		//前端排序
		if (sortList && sortList.length > 0) {
			tableData.value.sort((a: any, b: any) => {
				const valueA = a[field];
				const valueB = b[field];
				if (sortList[0].order === 'asc') {
					return valueA > valueB ? 1 : -1;
				} else {
					return valueA < valueB ? 1 : -1;
				}
			});
		}
	}
};

//单元格点击事件
const cellClick = ({ row }: any) => {
	emit('cellClick', row);
};

//复选框选中事件
const checkboxChange = (res: any) => {
	const records = tableRef.value!.getCheckboxRecords();
	emit('select', records);
};

//全选
const checkboxAll = ({ records }: any) => {
	emit('select', records);
};

//范围选择结束
const checkboxRangeEnd = ({ records }: any) => {
	emit('select', records);
};
//格式化表尾数据
const formatSummary = (summary: any) => {
	if (!summary) return [];
	let result;

	if (!Array.isArray(summary)) {
		let summaryKeys: string[] = [];
		props.tableCols.forEach((item: VxeTable.Columns) => {
			if (item.children && item.children.length > 0) {
				item.children.forEach((child: VxeTable.Columns) => {
					if (summary[`${child.field}_sum`] !== null && summary[`${child.field}_sum`] !== undefined) {
						summaryKeys.push(child.field as string);
					}
				});
			} else {
				if (summary[`${item.field}_sum`] !== null && summary[`${item.field}_sum`] !== undefined) {
					summaryKeys.push(item.field as string);
				}
			}
		});
		//找出表头中有合计的字段,并且匹配formatter和field
		let fieldsArr = props.tableCols
			.filter((item) => summaryKeys.includes(item.field as any))
			.map((item) => {
				return {
					field: item.field,
					formatter: item.formatter,
				};
			});

		props.tableCols.forEach((item: any) => {
			if (item.children && item.children.length > 0) {
				item.children.forEach((child: any) => {
					if (summaryKeys.includes(child.field as any)) {
						fieldsArr.push({
							field: child.field,
							formatter: child.formatter,
						});
					}
				});
			}
		});
		result = summaryKeys.reduce((acc: any, key: string, index: number) => {
			const res = fieldsArr?.find((item: any) => item.field == key);
			//有formatter并且是字符串,只允许匹配formatters里面对应的格式化方法
			if (res?.formatter && typeof fieldsArr[index].formatter === 'string') {
				//根据对应的字段格式化数据
				acc[key] = formatters.fmtNum(summary[`${key}_sum`]);
			} else {
				acc[key] = formatters.fmtNum(summary[`${key}_sum`]);
			}
			return acc;
		}, {});
	} else {
		let summaryKeys = Object.keys(summary[0]);
		let fieldsArr = props.tableCols
			.filter((item) => summaryKeys.includes(item.field as any))
			.map((item) => {
				return {
					field: item.field,
					formatter: item.formatter,
				};
			});
		props.tableCols.forEach((item: any) => {
			if (item.children && item.children.length > 0) {
				item.children.forEach((child: any) => {
					if (summaryKeys.includes(child.field as any)) {
						fieldsArr.push({
							field: child.field,
							formatter: child.formatter,
						});
					}
				});
			}
		});
		result = summary.map((item: any) => {
			Object.keys(item).forEach((keys: string) => {
				keys = keys.replace(/_sum$/, '');
				const formatter = fieldsArr.find((field) => field.field === keys)?.formatter;
				item[keys] = formatter && typeof formatter === 'string' ? formatters.fmtNum(item[keys]) : formatters.fmtNum(item[keys]);
			});
			return item;
		});
	}
	return Array.isArray(summary) ? result : [result];
};

// 工具栏按钮点击事件
const tooclick = async (params: any) => {
	if (params.type == 'reset' || params.type == 'confirm') {
		await getList();
	}
};

// 表尾单元格点击事件
interface Column {
	field?: string;
	cols?: Column[];
	summaryEvent?: boolean;
}

const findcol = (cols: Column[], property: string): Column | undefined => {
	let column: Column | undefined;
	for (let i = 0; i < cols.length; i++) {
		const c = cols[i];
		if (column) break;
		else if (c.field && c.field.toLowerCase() === property.toLowerCase()) {
			column = c;
			break;
		} else if (c.cols && c.cols.length > 0) {
			column = findcol(c.cols, property);
		}
	}
	return column;
};

const footerCellClick = ({ items, $rowIndex, column, columnIndex, $columnIndex, $event }: any): void => {
	const col = findcol(props.tableCols as Column[], column.property);
	if (col && col.summaryEvent) {
		emit('footerCellClick', items, column.property);
	}
};

const changeTableData = (data: any) => {
	tableData.value = data;
};

// 将表格和工具栏进行关联
nextTick(() => {
	const $table = tableRef.value;
	const $toolbar = toolbarRef.value;
	if ($table && $toolbar) {
		$table.connect($toolbar);
	}
});

const formats = (row: any, item: VxeTable.Columns): any => {
	if (item.formatter && item.formatter === 'formatLinkProcode') {
		return formatters.formatLinkProcode(row[item.field!], row.platform);
	}
	if (item && item.formatter && typeof item.formatter === 'string') {
		return formatters[item.formatter](row[item.field!]);
	}
};

const getCurrentRecord = () => {
	return tableRef.value!.getCurrentRecord();
};

//获取表格列信息
const getColumnsInfo = () => {
	const $table = tableRef.value;
	if ($table) {
		//获取表格列
		const columns = $table.getTableColumn(); //获取当前表格的列（collectColumn收集到的全量列、fullColumn全量表头列、tableColumn处理条件之后的全量表头列、visibleColumn当前渲染中的表头列）
		return columns;
	}
};

const setOrderBy = (info: any) => {
	query.value.orderBy = info.orderBy;
	query.value.isAsc = info.isAsc;
};

defineExpose({
	getList,
	query,
	tableData,
	getColumnsInfo, //获取表格列信息
	getCurrentRecord, //获取当前行数据
	onScrollToRow, //滚动到表格指定行
	onAssignedData, //表格数据回显
	clearSelection, //清空复选框选中
	expandAllTreeEvent, //展开树形表格
	clearTreeExpand, //清空树节点
	matchData, //修改表格数据
	showHidenColums, //显示隐藏列
	changeTableData, //修改表格数据
	refreshTable, //刷新表格数据
	setOrderBy,
});

onMounted(async () => {
	//如果需要掉接口,就调用接口
	if (props.isNeedQueryApi) {
		if (props.getCols) {
			await props.getCols();
			await getList();
		} else {
			await getList();
		}
	} else {
		tableData.value = props.data;
		listTotal.value = props.data.length;
	}
});
</script>
<style lang="scss" scoped>
.vxe-toolbar {
	padding: 0 0 5px 0;
}

::v-deep .vxe-table--render-default.size--mini .vxe-body--column.col--ellipsis > .vxe-cell,
.vxe-table--render-default.size--mini .vxe-footer--column.col--ellipsis > .vxe-cell,
.vxe-table--render-default.size--mini .vxe-header--column.col--ellipsis > .vxe-cell {
	max-height: none !important;
}

//滚动条样式
::v-deep .vxetableCss ::-webkit-scrollbar {
	width: 10px !important;
	height: 12px !important;
}

// :deep(.vxe-table--render-default:not(.is--empty).is--footer.is--scroll-x .vxe-table--body-wrapper) {
// 	overflow-x: hidden !important;
// }
:deep(.vxe-table--border-line) {
	z-index: 10;
}
</style>
