<template>
	<Container v-loading="loading">
		<template #header>
			<div class="topCss">
				<dataRange v-model:startDate="query.startTime" v-model:endDate="query.endTime" class="publicCss" style="width: 250px" :clearable="false" />
				<el-select v-model="query.personnelIds" placeholder="人员名称" class="public_Css" filterable clearable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false">
					<el-option v-for="item in props.directorlist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input v-model.trim="query.proCode" placeholder="产品ID" clearable maxlength="50" class="publicCss" />
				<el-select v-model="query.shopCodes" placeholder="店铺" class="public_Css" clearable filterable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false">
					<el-option v-for="item in props.shopList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<div class="pb5">
					<el-button type="primary" @click="getList">查询</el-button>
					<el-button type="primary" @click="exportProps">导出</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				id="dataStatistics202506271758"
				:tableCols="tableCols"
				:query="query"
				:order-by="'yearMonthDay'"
				:is-Asc="false"
				:query-api="DataStatisticsQuery"
				showsummary
				:tree-config="{ children: 'childBase' }"
			/>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, PropType } from 'vue';
import dayjs from 'dayjs';
import { DataStatisticsQuery, ExportDataStatisticsDY } from '/@/api/operatemanage/productManager';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const props = defineProps({
	shopList: {
		type: Array as PropType<any[]>,
		default: () => [],
	},
	directorlist: {
		type: Array as PropType<any[]>,
		default: () => [],
	},
});
const query = ref({
	startTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
	endTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
	personnelIds: [], //人员名称
	proCode: '', //产品ID
	shopCodes: [], //店铺
});
const table = ref();
const loading = ref(false);
const exportProps = async () => {
	loading.value = true;
	await ExportDataStatisticsDY({ ...query.value, ...table.value.query })
		.then((data: any) => {
			loading.value = false;
			const aLink = document.createElement('a');
			let blob = new Blob([data], { type: 'application/vnd.ms-excel' });
			aLink.href = URL.createObjectURL(blob);
			aLink.setAttribute('download', '剪辑人员数据统计导出' + new Date().toLocaleString() + '.xlsx');
			aLink.click();
		})
		.catch(() => {
			loading.value = false;
		});
};

const getList = async () => {
	table.value.refreshTable(true);
};
const tableCols = ref<VxeTable.Columns[]>([
	{ width: 'auto', field: 'yearMonthDay', title: '日期', formatter: 'formatDate', align: 'center', treeNode: true },
	{ width: 'auto', field: 'personnelName', title: '人员名称', align: 'center' },
	{ width: 'auto', field: 'groupName', title: '组', align: 'center' },
	{ width: 'auto', field: 'proCode', title: '产品ID', align: 'center' },
	{ width: 'auto', field: 'shopName', title: '店铺', align: 'center' },
	{ width: 'auto', sortable: true, field: 'advertisementFee', title: '广告消耗', align: 'center' },
]);
</script>

<style scoped lang="scss">
.public_Css {
	width: 185px;
	margin: 0 10px 5px 0;
}

::v-deep .el-select__tags-text {
	max-width: 70px;
}
</style>
