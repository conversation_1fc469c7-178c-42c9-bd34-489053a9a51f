import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API}/loginlog/`;

// 访问日志
export const PageReqLog = (module:string,page:string,tab:string,action:string, config = {}) => { 
    let p2={"ModuleName":module,"PageName":page,"PageTab":tab,"ActionName":action,"AppType":"新运营管理后台", ReqPath: ""};
    p2.ReqPath = window.location.pathname;
    return request.post(apiPrefix + 'PageReqLog', p2, config)
  }
  
