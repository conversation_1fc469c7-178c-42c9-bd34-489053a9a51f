// import request from '/@/utils/request';
import request from '/@/utils/yhrequest.ts'
// const apiPrefix = `${import.meta.env.VITE_APP_BASE_APIFOX}/48c632df-27f0-4bd4-9cbe-c1f06556e844/` //mock地址
const apiPrefix = `${import.meta.env.VITE_APP_BASE_APIFOX}/` //测试地址
import qs from 'qs'

// https://console-mock.apipost.cn/mock/48c632df-27f0-4bd4-9cbe-c1f06556e844/
// https://console-mock.apipost.cn/mock/48c632df-27f0-4bd4-9cbe-c1f06556e844/employeePage
// https://console-mock.apipost.cn/mock/8ff82815-9613-414c-bcc5-30311f128133?apipost_id=d75a7b

// https://console-mock.apipost.cn/mock/a7caca03-ede2-4a60-b1ed-5c9b4643b1aa/employeePage


////////岗位设置

//获取主列表
export const postPage = (params, config = {}) => {return request.post(apiPrefix + 'postPage',  params,  config )}


//导出
export const postExport = (params, config = { responseType: 'blob' }) => {
 return request.post(apiPrefix + 'postExport', params, config)
}

//导入
export const postImport = (params, config = {}) => {return request.post(apiPrefix + 'postImport',  params,  config )}

//删除
// export const postRemove = (params, config = {}) => {return request.post(apiPrefix + 'postRemove?ids='+params.ids,  params,  config )}
export const postRemove = (params) => {
 const queryString = qs.stringify(params);
 return request.post(apiPrefix + 'postRemove'+'?'+queryString)
}

//新增或者修改
export const postSubmit = (params, config = {}) => {return request.post(apiPrefix + 'postSubmit',  params,  config )}

//获取字段列表
export const postListValue = (params, config = {}) => {return request.post(apiPrefix + 'postListValue?fieldName='+params,  params,  config )}

export const priceStatisticsListValue = (params, config = {}) => {return request.post(apiPrefix + 'priceStatisticsListValue?fieldName='+params,  params,  config )}

export const operateStratifyMainListValue = (params, config = {}) => {return request.post(apiPrefix + 'operateStratifyMainListValue?fieldName='+params,  params,  config )}

export const operateStratifyMainStatisticListValue = (params, config = {}) => {return request.post(apiPrefix + 'operateStratifyMainStatisticListValue?fieldName='+params,  params,  config )}


// /yh-xz-salary/departmentList
//获取所有部门
export const departmentList = (params) => {
 const queryString = qs.stringify(params);
 return request.post(apiPrefix + 'departmentList'+'?'+queryString)
}
//获取后面等级部门
export const departmentAfterList = (params) => {
 const queryString = qs.stringify(params);
 return request.post(apiPrefix + 'departmentAfterList'+'?'+queryString)
}

//获取所有区域 传1
export const departmentListByParentId = (params, config = {}) => {return request.post(apiPrefix + 'departmentListByParentId',  params,  config )}

//获取仓库列表
export const warehouseList = (params, config = {}) => {return request.post(apiPrefix + 'warehouseList',  params,  config )}

/////////班次设置
//获取主列表
// postPage
export const shiftPage = (params, config = {}) => {return request.post(apiPrefix + 'shiftPage',  params,  config )}

//获取岗位编码
export const shiftList = (params, config = {}) => {return request.post(apiPrefix + 'shiftList',  params,  config )}

//导出
// postExport
export const shiftExport = (params, config = { responseType: 'blob' }) => {
 return request.post(apiPrefix + 'shiftExport', params, config)
}

//编辑
// postSubmit

export const shiftSubmit = (params, config = {}) => {return request.post(apiPrefix + 'shiftSubmit',  params,  config )}


// 获取下拉值
// postListValue
export const shiftListValue = (params, config = {}) => {return request.post(apiPrefix + 'shiftListValue?fieldName='+params,  params,  config )}

//导入
export const shiftImport = (params, config = {}) => {return request.post(apiPrefix + 'shiftImport',  params,  config )}

//删除
export const shiftRemove = (params) => {
 const queryString = qs.stringify(params);
 return request.post(apiPrefix + 'shiftRemove'+'?'+queryString)
}


///////////////部门设置
// 获取部门列表
export const departmentRelationPage = (params, config = {}) => {return request.post(apiPrefix + 'departmentRelationPage',  params,  config )}

//部门左右关联
export const departmentRelationUpdate = (params, config = {}) => {return request.post(apiPrefix + 'departmentRelationUpdate',  params,  config )}

//批量部门左右关联
export const departmentRelationBatch = (params, config = {}) => {return request.post(apiPrefix + 'departmentRelationBatch',  params,  config )}

//部门删除
export const departmentRelationRemove = (params) => {
 const queryString = qs.stringify(params);
 return request.post(apiPrefix + 'departmentRelationRemove'+'?'+queryString)
}

/////////////////////////基础薪资

//导入
export const basisSalaryImport = (params, config = {}) => {return request.post(apiPrefix + 'basisSalaryImport',  params,  config )}

//导出
// basisSalaryExport
export const basisSalaryExport = (params, config = { responseType: 'blob' }) => {
 return request.post(apiPrefix + 'basisSalaryExport', params, config)
}

//获取主列表
export const basisSalaryPage = (params, config = {}) => {return request.post(apiPrefix + 'basisSalaryPage',  params,  config )}

//编辑
export const basisSalarySubmit = (params, config = {}) => {return request.post(apiPrefix + 'basisSalarySubmit',  params,  config )}

//获取模板
export const salaryTemplateList = (params, config = {}) => {return request.post(apiPrefix + 'salaryTemplateList',  params,  config )}

//////////////////////////薪资模板

//导入
export const salaryTemplateImport = (params, config = {}) => {return request.post(apiPrefix + 'salaryTemplateImport',  params,  config )}

//导出
export const salaryTemplateExport = (params, config = { responseType: 'blob' }) => {
 return request.post(apiPrefix + 'salaryTemplateExport', params, config)
}

//获取主列表
export const salaryTemplatePage = (params, config = {}) => {return request.post(apiPrefix + 'salaryTemplatePage',  params,  config )}

//编辑提交
export const salaryTemplateSubmit = (params, config = {}) => {return request.post(apiPrefix + 'salaryTemplateSubmit',  params,  config )}


//操作日志
export const operationLogPage = (params, config = {}) => {return request.post(apiPrefix + 'operationLogPage',  params,  config )}

//岗位下拉
export const postList = (params, config = {}) => {return request.post(apiPrefix + 'postList?regionId='+params.regionId,  params,  config )}

//采购涨价降价下拉
export const procurePurchaseDataListValue = (params, config = {}) => {return request.post(apiPrefix + 'procurePurchaseDataListValue?fieldName='+params,  params,  config )}


//获取所有部门
export const updateDepartmentStatus = (params) => {
    const queryString = qs.stringify(params);
    return request.post(apiPrefix + 'updateDepartmentStatus'+'?'+queryString)
   }

// 查询取值列信息
export const procureCommissionSettingPlateSelect = () => {return request.get(apiPrefix + 'procureCommissionSettingPlateSelect' )}


export const recordList = (params, config = {}) => {return request.post(apiPrefix + 'recordList',  params,  config )}


export const recordListExport = (params, config = {}) => {return request.post(apiPrefix + 'recordListExport',  params,  config )}


//店铺下拉
export const getOperateShopList = (params, config = {}) => {return request.post(apiPrefix + 'getOperateShopList',  params,  config )}

