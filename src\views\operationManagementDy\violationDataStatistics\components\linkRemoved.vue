<template>
	<Container v-loading="listLoading">
		<template #header>
			<div class="topCss">
				<el-input v-model.trim="leftQuery.styleCode" placeholder="系列编码" clearable maxlength="50" class="publicCss" />
				<el-select v-model="leftQuery.platform" placeholder="平台" class="publicCss" clearable filterable @change="shopInit">
					<el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.shopCodes" placeholder="店铺名称" class="public_Css" clearable filterable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false">
					<el-option v-for="item in shopList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input v-model.trim="query.shopCode" placeholder="店铺ID" class="publicCss" clearable maxlength="50" />
				<el-select v-model="query.groupIds" placeholder="运营组" class="public_Css" clearable filterable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false">
					<el-option v-for="item in props.groupList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<el-select v-model="query.operateSpecialIds" placeholder="运营专员" class="public_Css" filterable clearable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false">
					<el-option v-for="item in props.directorlist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.user1Ids" placeholder="运营助理" class="public_Css" filterable clearable multiple collapse-tags collapse-tags-tooltip :reserve-keyword="false">
					<el-option v-for="item in props.directorlist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<div class="pb5" style="display: flex; justify-content: space-between; align-items: center; flex: 1">
					<div>
						<el-button type="primary" @click="getList">查询</el-button>
						<el-button type="primary" @click="onAdd" v-auth="'LinkRemovedAddPermission'">新增</el-button>
					</div>
					<div style="margin-left: auto">
						<el-button type="primary" @click="batchOperation(1)" v-auth="'LinkRemovedBatchShelfManagementPermission'">批量上下架</el-button>
						<el-button type="primary" @click="batchOperation(2)" v-auth="'LinkRemovedBatchShelfManagementPermission'">导出上下架日志</el-button>
					</div>
				</div>
			</div>
		</template>
		<template #content>
			<div class="tables-container">
				<div class="left-table">
					<vxetable
						ref="leftTable"
						id="202506121130"
						:tableCols="tableCols1"
						showsummary
						isIndexFixed
						:query="leftQuery"
						:query-api="ViolationDataStatisticsStyleCodeQuery"
						:isNeedPager="false"
						:pageSize="100000"
					>
						<template #toolbar_buttons>
							<el-button @click="exportProps" type="primary">导出</el-button>
						</template>
					</vxetable>
				</div>
				<div class="right-table">
					<vxetable
						ref="table"
						id="202506120930"
						:tableCols="tableCols"
						showsummary
						isIndexFixed
						:query="query"
						:query-api="ViolationDataStatisticsQueryProduct"
						isNeedCheckBox
						@select="onCheckBoxMethod"
					/>
				</div>
			</div>
			<el-dialog v-model="addDialogVisible" title="新增" width="400px" draggable overflow :close-on-click-modal="false" :destroy-on-close="true">
				<el-form :model="addForm" label-width="100px" :rules="addFormRules" ref="addFormRef" style="padding: 10px 0">
					<el-form-item label="系列编码" prop="styleCode">
						<el-input v-model.trim="addForm.styleCode" placeholder="请输入系列编码" clearable maxlength="50" show-word-limit />
					</el-form-item>
					<el-form-item label="平台" prop="platform">
						<el-select v-model="addForm.platform" placeholder="平台" clearable filterable>
							<el-option v-for="item in platformlist.filter((x) => x.label !== '未知')" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>
				</el-form>
				<template #footer>
					<div class="dialog-footer">
						<el-button @click="addDialogVisible = false">取消</el-button>
						<el-button type="primary" @click="onSave">确定</el-button>
					</div>
				</template>
			</el-dialog>
			<el-dialog v-model="batchListingDelistVisible" title="批量上下架" width="80%" draggable overflow :close-on-click-modal="false" :destroy-on-close="true">
				<batchListingDelist :checkdata="checkBoxList" v-if="batchListingDelistVisible" style="height: 650px" />
			</el-dialog>

			<el-dialog title="导出" v-model="exportracklogoutLogs" width="30%" draggable overflow>
				<div class="centered-content">
					<el-date-picker
						v-model="logtimeRanges"
						type="daterange"
						unlink-panels
						range-separator="至"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
						:clearable="false"
						style="width: 350px"
						value-format="YYYY-MM-DD"
						format="YYYY-MM-DD"
						:disabled-date="disabledDate"
						@change="changeTime"
					>
					</el-date-picker>
				</div>
				<div class="centered-content" style="color: red">*如导出日志超过100000条数据会导致系统卡顿或长时间无响应,请刷新界面重新导出</div>
				<div class="centered-content">
					<el-button @click="exportracklogoutLogs = false" style="margin-right: 20px">关闭</el-button>
					<el-button type="primary" @click="onExportLogs">确定</el-button>
				</div>
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted, nextTick, PropType } from 'vue';
import {
	ViolationDataStatisticsStyleCodeQuery,
	ViolationDataStatisticsQueryProduct,
	ViolationDataStatisticsStyleCodeAdd,
	ViolationDataStatisticsStyleCodeDelete,
	GetProductUpDownLogExport,
	ExportViolationDataStatisticsQueryProduct,
} from '/@/api/operatemanage/productManager';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
import { GetShopList } from '/@/api/operatemanage/shop';
import { ElMessage, ElMessageBox } from 'element-plus';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const batchListingDelist = defineAsyncComponent(() => import('/@/components/yhCom/batchListingDelist.vue'));
import { platformlist } from '/@/utils/tools';
interface OptionItem {
	value: string | number;
	label: string;
}
const props = defineProps({
	groupList: {
		type: Array as PropType<OptionItem[]>,
		default: () => [],
	},
	directorlist: {
		type: Array as PropType<OptionItem[]>,
		default: () => [],
	},
});

const disabledDate = (time: Date) => {
	return time.getTime() > Date.now();
};
const query = ref({
	platform: null as any,
	shopCodes: [],
	shopCode: '',
	groupIds: [],
	operateSpecialIds: [],
	user1Ids: [],
	styleCode: '',
});
const leftQuery = ref({
	styleCode: '',
	platform: '',
});
const addForm = ref({
	styleCode: '',
	platform: null as any,
});
const addFormRules = ref({
	styleCode: [{ required: true, message: '请输入系列编码', trigger: 'blur' }],
	platform: [{ required: true, message: '请选择平台', trigger: 'blur' }],
});
const derivationtime = ref({
	upDownOperateStartDate: '',
	upDownOperateEndDate: '',
});
const logtimeRanges = ref<any[]>([]);
const exportracklogoutLogs = ref(false);
const listLoading = ref(false);
const addDialogVisible = ref(false);
const addFormRef = ref();
const shopList = ref<any[]>([]);
const table = ref();
const leftTable = ref();
const checkBoxList = ref<any[]>([]);
const batchListingDelistVisible = ref(false);

const changeTime = (e: any) => {
	if (!e || !e[0] || !e[1]) return;

	// 确保日期格式正确
	const startDate = dayjs(e[0]).isValid() ? dayjs(e[0]) : dayjs();
	const endDate = dayjs(e[1]).isValid() ? dayjs(e[1]) : dayjs();

	// 计算日期差
	const diffDays = endDate.diff(startDate, 'day');

	// 如果超过7天，自动调整为7天
	if (diffDays > 7) {
		ElMessage.error('日期区间不能超过7天,已为您自动调整为7天');
		const newEndDate = startDate.add(6, 'day');
		logtimeRanges.value = [startDate.format('YYYY-MM-DD'), newEndDate.format('YYYY-MM-DD')];
	} else {
		logtimeRanges.value = [startDate.format('YYYY-MM-DD'), endDate.format('YYYY-MM-DD')];
	}

	// 设置导出时间范围
	derivationtime.value.upDownOperateStartDate = logtimeRanges.value[0];
	derivationtime.value.upDownOperateEndDate = logtimeRanges.value[1];
};

const exportProps = async () => {
	listLoading.value = true;
	await ExportViolationDataStatisticsQueryProduct({ ...query.value, ...table.value.query, styleCode: leftQuery.value.styleCode, platform: leftQuery.value.platform })
		.then((data: any) => {
			listLoading.value = false;
			const aLink = document.createElement('a');
			let blob = new Blob([data], { type: 'application/vnd.ms-excel' });
			aLink.href = URL.createObjectURL(blob);
			aLink.setAttribute('download', '违规链接下架导出数据' + new Date().toLocaleString() + '.xlsx');
			aLink.click();
		})
		.catch(() => {
			listLoading.value = false;
		});
};

const batchOperation = (type: number) => {
	if (type == 1) {
		// 保留(拼多多/天猫/淘宝/抖音/京东/快手)的数据
		let oldLength = checkBoxList.value.length;
		checkBoxList.value = checkBoxList.value.filter((item) => {
			return item.platform === 1 || item.platform === 2 || item.platform === 6 || item.platform === 7 || item.platform === 9 || item.platform === 14;
		});
		if (checkBoxList.value.length < oldLength && checkBoxList.value.length == 0) {
			ElMessage.error('未选择(拼多多/天猫/淘宝/抖音/京东/快手)的数据!');
			return;
		} else if (checkBoxList.value.length < oldLength) {
			ElMessage.warning('已过滤掉平台非(拼多多/天猫/淘宝/抖音/京东/快手)的数据，正在加载编辑页...');
			setTimeout(() => {
				batchListingDelistVisible.value = true;
			}, 2000);
			return;
		} else if (checkBoxList.value.length == 0) {
			ElMessage.error('请选择平台为(拼多多/天猫/淘宝/抖音/京东/快手)的数据!');
			return;
		}
		batchListingDelistVisible.value = true;
	} else {
		exportracklogoutLogs.value = true;
		const yesterday = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
		logtimeRanges.value = [yesterday, yesterday];
		derivationtime.value.upDownOperateStartDate = yesterday;
		derivationtime.value.upDownOperateEndDate = yesterday;
	}
};

const onExportLogs = async () => {
	listLoading.value = true;
	const paramsList = query.value;
	paramsList.platform = 2; //拼多多
	const params = { ...paramsList, ...derivationtime.value };
	const { success, msg } = await GetProductUpDownLogExport(params);
	if (success) {
		ElMessage.success(msg || '导出成功');
	} else {
		ElMessage.error(msg || '导出失败');
	}
	exportracklogoutLogs.value = false;
	listLoading.value = false;
};
const onCheckBoxMethod = (val: any) => {
	checkBoxList.value = val;
};

const onAdd = () => {
	addDialogVisible.value = true;
	nextTick(() => {
		addFormRef.value.clearValidate();
		addForm.value.styleCode = '';
		addForm.value.platform = null;
	});
};

const onSave = () => {
	addFormRef.value.validate(async (valid: any) => {
		if (valid) {
			const { success } = await ViolationDataStatisticsStyleCodeAdd(addForm.value);
			if (success) {
				ElMessage.success('新增成功');
				addDialogVisible.value = false;
				leftTable.value.refreshTable(true);
			}
		}
	});
};

const handleDelete = (row: any) => {
	ElMessageBox.confirm('确定删除吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		const { success } = await ViolationDataStatisticsStyleCodeDelete(row);
		if (success) {
			ElMessage.success('删除成功');
			leftTable.value.refreshTable(true);
		}
	});
};

const openDetails = (row: any) => {
	query.value.styleCode = row.styleCode;
	query.value.platform = row.platform;
	table.value.refreshTable(true);
};

const getList = () => {
	query.value.styleCode = leftQuery.value.styleCode;
	query.value.platform = leftQuery.value.platform;
	table.value.refreshTable(true);
	table.value.clearSelection();
	checkBoxList.value = [];
	leftTable.value.refreshTable(true);
};

const tableCols1 = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'styleCode', title: '系列编码', type: 'click', copy: true, handle: (row: any) => openDetails(row) },
	{ field: 'platform', title: '平台', align: 'center', formatter: 'formatPlatform' },
	{
		field: 'refundRateBeforeShipment',
		align: 'right',
		title: '发货前退款率',
		formatter: (row: any) => (row.refundRateBeforeShipment || row.refundRateBeforeShipment === 0 ? row.refundRateBeforeShipment + '%' : ''),
	},
	{
		field: 'refundRateAfterShipment',
		align: 'right',
		title: '发货后退款率',
		formatter: (row: any) => (row.refundRateAfterShipment || row.refundRateAfterShipment === 0 ? row.refundRateAfterShipment + '%' : ''),
	},
	{
		title: '操作',
		align: 'center',
		width: '60',
		type: 'btnList',
		minWidth: '60',
		field: 'operation',
		btnList: [{ title: '删除', handle: handleDelete }],
		fixed: 'right',
	},
]);
const tableCols = ref<VxeTable.Columns[]>([
	{ field: 'platform', title: '平台', align: 'center', formatter: 'formatPlatform' },
	{ field: 'shopName', align: 'center', title: '店铺名称' },
	{ field: 'shopCode', align: 'center', title: '店铺ID' },
	{ field: 'groupId', align: 'center', title: '运营组', formatter: (row: any) => row.groupName || '' },
	{ field: 'operateSpecialUserId', align: 'center', title: '运营专员', formatter: (row: any) => row.operateSpecialUserName || '' },
	{ field: 'userId', align: 'center', title: '运营助理', formatter: (row: any) => row.userRealName || '' },
	{ field: 'proCode', align: 'center', title: '产品ID' },
]);
const shopInit = async (e: any) => {
	const { data: data0, success: success0 } = await GetShopList({ platform: e, currentPage: 1, PageSize: 100000 });
	if (success0) {
		shopList.value = data0.list.map((item: any) => {
			return {
				value: item.shopCode,
				label: item.shopName,
			};
		});
	}
};
onMounted(async () => {
	await shopInit('');
});
</script>

<style scoped lang="scss">
.public_Css {
	width: 190px;
	margin: 0 10px 5px 0;
}

::v-deep .el-select__tags-text {
	max-width: 65px;
}

.tables-container {
	display: flex;
	gap: 10px;
	width: 100%;
	height: 100%;
}

.left-table {
	width: 28%;
}

.right-table {
	flex: 1;
}

.dialog-footer {
	display: flex;
	justify-content: center;
	gap: 10px;
	padding: 10px 20px 20px 20px;
}

.centered-content {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 25px;
}
</style>
