<template>
  <Container>
    <template #content>
      <el-tabs v-model="activeName" class="demo-tabs w100 h100">
        <el-tab-pane label="消费者负向体验" name="first" class="h100" lazy>
          <pddDeductionAppeal />
        </el-tab-pane>
      </el-tabs>
    </template>
  </Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted } from 'vue';
const activeName = ref('first');
const pddDeductionAppeal = defineAsyncComponent(() => import('./components/pddDeductionAppeal.vue'));
onMounted(async () => {

});
</script>

<style scoped lang="scss"></style>
