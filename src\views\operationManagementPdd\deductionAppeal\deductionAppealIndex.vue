<template>
  <Container>
    <template #content>
      <el-tabs v-model="activeName" class="demo-tabs w100 h100">
        <el-tab-pane label="消费者负向体验" name="first" class="h100" lazy>
          <pddDeductionAppeal />
        </el-tab-pane>
      </el-tabs>
    </template>
  </Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted } from 'vue';
const activeName = ref('first');
const pddDeductionAppeal = defineAsyncComponent(() => import('./components/pddDeductionAppeal.vue'));
import {GetShopList } from '/@/api/operatemanage/shop';
const shopList = ref<any[]>([]);
onMounted(async () => {
  //店铺
  const { data: data0, success: success0 } = await GetShopList({ platform: 2, currentPage: 1, PageSize: 100000 });
  if (success0) {
    shopList.value = data0.list.map((item: any) => {
      return {
        value: item.shopCode,
        label: item.shopName,
        platform: item.platform,
      };
    });
  }
});
</script>

<style scoped lang="scss"></style>
