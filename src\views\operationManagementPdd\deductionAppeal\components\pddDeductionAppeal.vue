<template>
  <div style="height: 100%;">
    <Container v-loading="loading">
      <template #header>
        <div class="topCss">
          <div class="filter-row">
            <div class="filter-group">
              <label>数据导入时间:</label>
              <dataRange v-model:startDate="query.importStartTime" v-model:endDate="query.importEndTime"
                         class="filter-item" style="width: 250px" :clearable="false" />
            </div>

            <div class="filter-group">
              <label>店铺:</label>
              <shopSelect v-model:valueList="query.shopCodes" multiple field="shopCode"
                          class="filter-item" style="width: 290px" />
            </div>

            <div class="filter-group">
              <label>店铺订单号:</label>
              <manyInput v-model:inputt="query.orderNos" title="店铺订单号" :verifyNumber="false" :maxRows="1000" :maxlength="22000"
                         placeholder="多个订单用逗号分隔" class="filter-item" style="width: 200px" />
            </div>

            <div class="filter-group">
              <label>申诉截止时间:</label>
              <dataRange v-model:startDate="query.appealLastStartTime" v-model:endDate="query.appealLastEndTime"
                         class="filter-item" style="width: 250px" :clearable="false" />
            </div>
          </div>

          <div class="filter-row mt-10">
            <div class="filter-group">
              <label>是否同步:</label>
              <el-select v-model="query.isSync" placeholder="是否同步店铺后台" class="filter-item"
                         style="width: 160px;" clearable>
                <el-option v-for="item in statusList" :key="item.value"
                           :label="item.label" :value="item.value" />
              </el-select>
            </div>

            <div class="filter-group">
              <label>聊天记录搜索:</label>
              <manyInput v-model:inputt="query.keyWords" title="聊天记录搜索" :verifyNumber="false" :maxRows="1000" :maxlength="22000"
                         placeholder="关键词搜索" class="filter-item" style="width: 200px" />
            </div>

            <div class="filter-group">
              <label>判责类型:</label>
              <manyInput v-model:inputt="query.dutyType" title="判责类型" :verifyNumber="false" :maxRows="1000" :maxlength="22000"
                         placeholder="判责类型" class="filter-item" style="width: 200px" />
            </div>

            <div class="filter-group">
              <label>判责部门:</label>
              <el-select v-model="query.dutyDept" placeholder="判责部门" class="filter-item"
                         style="width: 160px;" multiple filterable clearable collapse-tags collapse-tags-tooltip>
                <el-option v-for="item in dutyDeptList" :key="item.value"
                           :label="item.label" :value="item.value" />
              </el-select>
            </div>

            <div class="action-buttons">
              <el-button type="primary" @click="table.refreshTable(true)">搜索</el-button>
              <el-button type="primary" @click="exportProps">导出</el-button>
              <el-button type="primary" @click="onAddMethod">配置</el-button>
            </div>
          </div>
        </div>
      </template>

      <template #content>
        <vxetable
            ref="table"
            id="deduction-appeal-table"
            :tableCols="tableCols"
            :query="query"
            :order-by="''"
            :is-Asc="false"
            :query-api="GetPddIllegalDeductionBadExperList"
            showsummary
            :treeConfig="{ childrenField: 'child', hasChildren: true }"
            isNeedDisposeProps
            @disposeProps="disposeProps"
        >
        </vxetable>
      </template>
    </Container>

    <!-- 配置弹窗 -->
    <el-dialog :title="editInfo.ruleTitle" v-model="editInfo.dialogVisible"
               width="60%" draggable style="margin-top: 10vh !important" custom-class="custom-dialog">
      <configPage />
    </el-dialog>

    <!-- 聊天记录弹窗 -->
    <el-dialog title="聊天记录" v-model="chatDialog.visible" width="60%" draggable>
      <vxetable
          ref="table"
          id="deduction-appeal-table"
          :tableCols="tableLtCols"
          :query="chatDialog"
          :order-by="''"
          :is-Asc="false"
          :query-api="GetPddIllegalDeductionOrderChatDataByOrderNo"
          showsummary
          :treeConfig="{ childrenField: 'child', hasChildren: true }"
          isNeedDisposeProps
          @disposeProps="disposeProps"
          :isNeedIndex="false"
          height="500"
      >
      </vxetable>

    </el-dialog>

    <!-- 申诉弹窗 -->
    <el-dialog title="发起申诉" v-model="appealDialog.visible" width="60%" draggable>
      <div style="height: 300px">
      <el-form :model="appealDialog.form" label-width="120px" >
        <el-form-item label="申诉原因" required>
          <el-radio v-model="appealDialog.form.appealReason" label="商品有问题，但与消费者达成一致" border>商品有问题，但与消费者达成一致</el-radio>
          <el-radio v-model="appealDialog.form.appealReason" label="商品有问题，已补偿消费者" border>商品有问题，已补偿消费者</el-radio>
          <el-radio v-model="appealDialog.form.appealReason" label="消费者未表达商品问题" border>消费者未表达商品问题</el-radio>
          <el-radio v-model="appealDialog.form.appealReason" label="商品无问题，消费者误解" border>商品无问题，消费者误解</el-radio>
          <el-radio v-model="appealDialog.form.appealReason" label="其他" border>其他</el-radio>
        </el-form-item>

        <el-form-item label="申诉说明" required>
          <el-input
              v-model="appealDialog.form.appealRemark"
              type="textarea"
              :rows="8"
              placeholder="请详细描述申诉原因，如已与消费者协商一致，并得到消费者认可"
              class="fixed-height-textarea"
          />
        </el-form-item>
        <el-form-item label="申诉凭证" required>
          <uploadMf v-model:imagesStr="appealDialog.form.appealImageArr" :upstyle="{ height: 40, width: 40 }" :limit="3" ref="refuploadMf" />
        </el-form-item>
      </el-form>
      </div>
      <template #footer>
        <el-button type="primary" v-if="appealDialog.form.canAppeal == 1" @click="submitAppeal(0)">保存</el-button>
        <el-button type="primary" v-if="appealDialog.form.canAppeal == 1" @click="submitAppeal(1)">提交</el-button>
        <el-button v-if="appealDialog.form.canAppeal == 1" @click="appealDialog.visible = false">取消</el-button>
        <el-button v-if="appealDialog.form.canAppeal != 1" @click="appealDialog.visible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted, defineAsyncComponent} from 'vue';
import dayjs from 'dayjs';
import { ElMessage } from 'element-plus';

import {
  GetPddIllegalDeductionBadExperList,
  ExportPddIllegalDeductionBadExperList,
  GetPddIllegalDeductionOrderChatDataByOrderNo,
  SavePddIllegalDeductionOrderAppealInfo, GetPddIllegalDeductionOrderAppealInfoByOrderNo
} from '/@/api/operatemanage/pddDeductionAppeal';
import configPage from "/@/views/operationManagementPdd/deductionAppeal/components/configPage.vue";
import {formatters} from "/@/utils/vxetableFormats";

const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const shopSelect = defineAsyncComponent(() => import('/@/components/yhCom/shopSelect.vue'));
const manyInput = defineAsyncComponent(() => import('/@/components/yhCom/manyInput.vue'));
const uploadMf = defineAsyncComponent(() => import('/@/components/yhCom/uploadMf.vue'));

// 查询条件
const query = ref({
  importStartTime: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
  importEndTime: dayjs().format('YYYY-MM-DD'),
  shopCodes: [],
  orderNos: undefined,
  appealLastTimeType: undefined,
  appealLastStartTime: undefined,
  appealLastEndTime: undefined,
  isSync: undefined,
  keyWords: undefined,
  dutyType: undefined,
  dutyDept: undefined,
});

const table = ref();
const loading = ref(false);
const isExport = ref(false);
// 状态选项
const statusList = ref([
  { label: '是', value: 1 },
  { label: '否', value: 0 },
]);

// 部门选项
const dutyDeptList = ref([
  { label: '运营部', value: '运营部' },
  { label: '采购部', value: '采购部' },
  { label: '客服部', value: '客服部' },
  { label: '仓库', value: '仓库' },
  { label: '其他', value: '其他' },
]);

// 配置弹窗
const editInfo = ref({
  ruleTitle: '配置',
  dialogVisible: false,
});

// 聊天记录弹窗
const chatDialog = ref({
  visible: false,
  loading: false,
  orderNo: ''
});

// 申诉弹窗
const appealDialog = ref({
  visible: false,
  loading: false,
  form: {
    appealCheck: undefined,
    orderNo: '',
    appealReason: '',
    appealRemark: '',
    appealImages: '' as string,
    appealImageArr: [] as string[],
    canAppeal: 0
  }
});

// 用户类型映射
const userTypeMap = {
  0: '消费者',
  1: '客服',
  2: '机器人',
  3: '系统'
};

const disposeProps = async (data: any, callback: Function) => {
  let a = data?.allData?.list || [];
  const formatItem = (item: any) => {
    item.yearMonthDayDate = dayjs(item.yearMonthDayDate).format('YYYY-MM-DD');
    item.orderCount = item.orderCount || 0;
    item.amountReductionPrice = item.amountReductionPrice ? Number(item.amountReductionPrice).toFixed(2) : '0.00';
  };
  a.forEach((item: any) => {
    formatItem(item);
    if (Array.isArray(item.child) && item.child.length > 0) {
      item.child.forEach(formatItem);
    }
  });
  setTimeout(() => {
    if (a?.length == 1) {
      if (table.value) {
        table.value.expandAllTreeEvent(true);
      }
    } else {
      if (table.value) {
        table.value.expandAllTreeEvent(false);
      }
    }
  }, 100);
  callback(data);
};


// 表格列配置
const tableCols = ref<VxeTable.Columns[]>([
  {
    sortable: true, width: 160, align: 'center',
    field: 'importTime', title: '数据导入时间',
    treeNode: true
  },
  { sortable: true, width: 160, align: 'center', field: 'shopName', title: '店铺' },
  { sortable: true, width: 160, align: 'center', field: 'orderNo', title: '商户订单号' },

  // 售后工作台数据列组
  {
    field: 'afterSaleGroup', title: '售后工作台数据', align: 'center',
    children: [
      { sortable: true, field: 'orderAmount', title: '订单金额', align: 'center', width: 90, formatter: 'fmtAmt2' },
      { sortable: true, field: 'refundAmount', title: '退款金额', align: 'center', width: 150, formatter: 'fmtAmt2' },
      { sortable: true, field: 'sendStatus', title: '发货状态', align: 'center', width: 115 },
      { sortable: true, field: 'saleAfterType', title: '售后类型', align: 'center', width: 90 },
      { sortable: true, field: 'saleAfterStatus', title: '售后状态', align: 'center', width: 150 },
      { sortable: true, field: 'applyTime', title: '申请时间', align: 'center', width: 115},
      { sortable: true, field: 'saleAfterDescribe', title: '售后申请说明', align: 'center', width: 90 },
    ]
  },

  // 消费者负向体验补偿列组
  {
    field: 'compensationGroup', title: '消费者负向体验补偿', align: 'center',
    children: [
      { sortable: true, field: 'questionType', title: '问题类型', align: 'center', width: 90 },
      { sortable: true, field: 'badExperCompensationCreatedTime', title: '创建时间', align: 'center', width: 150},
      { sortable: true, field: 'badExperCompensationStatus', title: '处理状态', align: 'center', width: 115 },
      { sortable: true, field: 'badExperCompensationReason', title: '补偿原因', align: 'center', width: 90 },
      { sortable: true, field: 'badExperCompensationBasis', title: '补偿依据', align: 'center', width: 150 },
      { sortable: true, field: 'badExperCompensationDescribe', title: '相关描述', align: 'center', width: 115 },
    ]
  },

  // 聊天记录操作列
  {
    field: 'chatActions', title: '聊天记录', align: 'center', width: 100,
    type: 'btnList',
    btnList: [
      {
        title: '查看',
        handle: (row) => showChatDialog(row.orderNo),
        isDisabled: (row) => !row.orderNo
      },
    ]
  },

  { sortable: true, width: 160, align: 'center', field: 'dutyType', title: '判责类型' },
  { sortable: true, width: 160, align: 'center', field: 'dutyDept', title: '判责部门' },
  { sortable: true, width: 160, align: 'center', field: 'appealLastTime', title: '剩余申诉时间' },

  // 申诉操作列
  {
    field: 'appealActions',
    title: '申诉',
    align: 'center',
    width: 120,
    type: 'btnList',
    btnList: [
      {
        title: '申诉',
        handle: (row) => showAppealDialog(row),
        isDisabled: (row) =>
            row.appealStatus === '已申诉' ||
            !row.orderNo ||
            row.canAppeal !== 1
      },
      {
        title: '查看',
        handle: (row) => showAppealDialog(row),
        isDisabled: (row) =>
            row.canAppeal == 1
      },
    ]
  },

  { sortable: true, width: 160, align: 'center', field: 'isSync', title: '是否同步店铺后台',
    formatter: (row: any) => row.isSyncStr
  },
  {
    sortable: true, width: 160, align: 'center',
    field: 'appealStatus', title: '申诉状态'
  },
]);

// 聊天表格列配置
const tableLtCols = ref<VxeTable.Columns[]>([
  { sortable: true,  align: 'center', field: 'userName', title: '用户名' },
  { sortable: true,  align: 'center', field: 'content', title: '聊天内容' },
  { sortable: true,  align: 'center', field: 'recordTime', title: '聊天时间' }
]);

// 显示聊天记录弹窗
const showChatDialog = async (orderNo: string) => {
  chatDialog.value.orderNo = orderNo;
  chatDialog.value.visible = true;
  chatDialog.value.loading = true;

  /*try {
    // 调用接口获取聊天记录
    const response = await GetPddIllegalDeductionOrderChatDataByOrderNo({ orderNo });
    const chatList = response.data?.list || [];

    // 处理数据格式
    chatDialog.value.data = chatList.map((item: any) => ({
      ...item,
      userTypeName: userTypeMap[item.userType] || '未知' // 转换用户类型为文本
    }));
    // 如果没有数据，显示提示
    if (chatDialog.value.data.length === 0) {
      ElMessage.info('该订单暂无聊天记录');
    }
  } catch (error) {
    console.error('获取聊天记录失败:', error);
    ElMessage.error('获取聊天记录失败，请稍后重试');
  } finally {
    chatDialog.value.loading = false;
  }*/
};

// 显示申诉弹窗
const showAppealDialog = async (row: any) => {
  // 初始化弹窗表单
  appealDialog.value.form = {
    appealCheck: undefined,
    orderNo: row.orderNo,
    appealReason: '',
    appealRemark: '',
    appealImages: '', // 初始化为空字符串
    appealImageArr: [], // 初始化为空数组
    canAppeal: 0
  };
  appealDialog.value.visible = true;
  appealDialog.value.loading = true;

  try {
    // 调用接口查询申诉信息
    const response = await GetPddIllegalDeductionOrderAppealInfoByOrderNo({
      orderNo: row.orderNo
    });

    const appealInfo = response.data;
    if (appealInfo) {
      // 处理申诉图片：将逗号拼接字符串分割为数组
      const imagesStr = appealInfo.appealImages || '';
      const imagesArr = imagesStr ? imagesStr.split(',') : [];

      // 回显数据（重点处理图片数组）
      appealDialog.value.form = {
        appealCheck: appealInfo.appealCheck,
        orderNo: row.orderNo,
        appealReason: appealInfo.appealReason || '',
        appealRemark: appealInfo.appealRemark || '',
        appealImages: imagesStr,
        appealImageArr: imagesArr,
        canAppeal: row.canAppeal
      };
    }
  } catch (error) {
    console.error(`查询订单${row.orderNo}的申诉信息失败:`, error);
    ElMessage.warning('查询历史申诉信息失败，可重新填写');
  } finally {
    appealDialog.value.loading = false;
  }
};

// 提交申诉
const submitAppeal = async (appealCheck: number) => {
  appealDialog.value.form.appealImages = appealDialog.value.form.appealImageArr.join(",");
  const form = { ...appealDialog.value.form };
  const submitData = { ...form };
  if (appealCheck === 1) {
    submitData.appealCheck = appealCheck;
  }
  // 提交数据
  loading.value = true;
  try {
    await SavePddIllegalDeductionOrderAppealInfo(submitData).then(res =>{
      if(res.success){
        ElMessage.success(
                appealCheck === 1 ? "申诉提交成功" : "申诉保存成功"
            );
      }
    });
    appealDialog.value.visible = false;
    table.value.refreshTable(true); // 刷新列表
  } catch (error: any) {
    // 错误处理
    const errorMsg = error.response?.data?.message || "操作失败，请稍后重试";
    ElMessage.error(errorMsg);
  } finally {
    loading.value = false;
  }
};

// 日期格式化
const formatDate = (dateStr: string) => {
  return dateStr ? dayjs(dateStr).format('YYYY-MM-DD HH:mm') : '';
};

// 导出数据
const exportProps = async () => {
  const params = { ...table.value.query, ...query.value };
  try {
    const data = await ExportPddIllegalDeductionBadExperList(params);
    ElMessage.success("下载任务已创建，稍后点击头像-下载管理，进行下载！");
  } catch (error) {
    ElMessage.error('导出失败，请稍后重试');
    console.error('导出数据失败:', error);
  } finally {
    isExport.value = false;
    loading.value = false; // 移除加载状态
  }
};



// 打开配置弹窗
const onAddMethod = () => {
  editInfo.value.dialogVisible = true;
};

// 初始化数据
onMounted(() => {
  // 默认展开第一级
  setTimeout(() => {
    if (table.value) {
      table.value.expandAllTreeEvent(true);
    }
  }, 500);
});
</script>

<style scoped lang="scss">
.topCss {
  padding: 15px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;

  label {
    font-size: 14px;
    color: #606266;
    white-space: nowrap;
  }
}

.filter-item {
  margin-bottom: 0;
}

.action-buttons {
  margin-left: auto;
  display: flex;
  gap: 10px;
}

.mt-10 {
  margin-top: 10px;
}

:deep(.el-table__expand-column .cell) {
  padding-left: 10px;
}

.custom-dialog .el-dialog__body {
  max-height: 600px; /* 固定最大高度 */
  overflow-y: auto;
}

:deep(.fixed-height-textarea .el-textarea__inner) {
  height: 150px !important;
  resize: none !important;
  overflow-y: auto;
}

:deep(.fixed-height-textarea .el-textarea) {
  min-height: 150px !important;
  max-height: 150px !important;
}

</style>
