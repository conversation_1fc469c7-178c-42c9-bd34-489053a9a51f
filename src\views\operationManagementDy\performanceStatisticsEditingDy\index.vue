<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" class="demo-tabs w100 h100">
				<el-tab-pane label="剪辑人员配置" name="first" class="h100">
					<personnelConfiguration :shopList="shopList" :directorlist="directorlist" />
				</el-tab-pane>
				<el-tab-pane label="数据统计" name="second" class="h100" lazy>
					<dataStatistics :shopList="shopList" :directorlist="directorlist" />
				</el-tab-pane>
			</el-tabs>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted } from 'vue';
const activeName = ref('first');
const personnelConfiguration = defineAsyncComponent(() => import('./components/personnelConfiguration.vue'));
const dataStatistics = defineAsyncComponent(() => import('./components/dataStatistics.vue'));
import { GetShopList, getDirectorList } from '/@/api/operatemanage/shop';
const shopList = ref<any[]>([]);
const directorlist = ref<any[]>([]);
onMounted(async () => {
	const res = await GetShopList({ platform: 6, CurrentPage: 1, PageSize: 100000 });
	shopList.value = res.data.list.map((x: any) => {
		return { value: x.shopCode, label: x.shopName };
	});
	const { data: data2, success: success2 } = await getDirectorList();
	if (data2 && success2) {
		directorlist.value = data2?.map((item: any) => {
			return {
				label: item.value,
				value: item.key,
			};
		});
	}
});
</script>

<style scoped lang="scss"></style>
