import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_BookKeeper}/SelectionProductCenterSaleDataPlanA/`;

//查询非架构售卖明细
export const GetNoJiaGouShouMaiGoods = (params: any, config = {}) => {
	return request.post(`${apiPrefix}GetNoJiaGouShouMaiGoods`, params, config);
};

//导出非架构售卖明细
export const ExportNoJiaGouShouMaiGoods = (params: any, config: any = { responseType: 'blob' }) => {
	return request.post(`${apiPrefix}ExportNoJiaGouShouMaiGoods`, params, config);
};

//查询非架构售卖面板
export const GetNoJiaGouShouMaiGoodsSum = (params: any, config = {}) => {
	return request.post(`${apiPrefix}GetNoJiaGouShouMaiGoodsSum`, params, config);
};
