import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_OperateManage}/OperateTool/`;

export const PageShopAspectListAsync = (params: any, config = {}) => {
	return request.post(apiPrefix + 'PageShopAspectListAsync', params, config);
};

//拼多多中台管理-导出
export const ExportShopAspectListAsync = (params: any, config = { responseType: 'blob' as any }) => {
	return request.post(`${apiPrefix}ExportShopAspectListAsync`, params, config);
};

//ID维度预警商品页面
export const PageIDAspectListAsync = (params: any, config = {}) => {
	return request.post(apiPrefix + 'PageIDAspectListAsync', params, config);
};

//ID维度预警商品页面导出
export const ExportIDAspectListAsync = (params: any, config = { responseType: 'blob' as any }) => {
	return request.post(`${apiPrefix}ExportIDAspectListAsync`, params, config);
};

//ID维度违规商品
export const PageIDAspectVioInfoAsync = (params: any, config = {}) => {
	return request.post(apiPrefix + 'PageIDAspectVioInfoAsync', params, config);
};

//ID维度违规商品导出
export const ExportIDAspectVioListAsync = (params: any, config = { responseType: 'blob' as any }) => {
	return request.post(`${apiPrefix}ExportIDAspectVioListAsync`, params, config);
};
