<template>
	<Container v-loading="loading">
		<template #header>
			<div class="topCss">
				<el-input v-model.trim="query.goodsName" placeholder="商品名称" clearable maxlength="50" class="publicCss" />
				<div class="numRangeCss">
					<div>价格</div>
					<numRange v-model:maxNum="query.priceMax" v-model:minNum="query.priceMin" class="publicCss" minPlaceHolder="最小值" maxPlaceHolder="最大值" :precision="2" :max="999999" :min="0" />
				</div>
				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
					<el-button type="primary" @click="exportProps">导出</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				id="opportunityGoodPdd202507201102"
				:tableCols="tableCols"
				:query="query"
				:query-api="GetHotSaleGoodsPddJHSPList"
				showsummary
				isNeedDisposeProps
				@disposeProps="props.disposeProps"
			/>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, PropType } from 'vue';
import { GetHotSaleGoodsPddJHSPList, ExportHotSaleGoodsPddJHSPList } from '/@/api/operatemanage/hotSaleGoodsData';
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
const props = defineProps({
	disposeProps: {
		type: Function as PropType<(data: any, callback: Function) => void>,
		required: true,
	},
});
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const query = ref({
	goodsName: '', //商品名称
	priceMax: undefined, //价格最大值
	priceMin: undefined, //价格最小值
});
const table = ref();
const loading = ref(false);
const exportProps = async () => {
	loading.value = true;
	await ExportHotSaleGoodsPddJHSPList({ ...query.value, ...table.value.query })
		.then((data: any) => {
			loading.value = false;
			const aLink = document.createElement('a');
			let blob = new Blob([data], { type: 'application/vnd.ms-excel' });
			aLink.href = URL.createObjectURL(blob);
			aLink.setAttribute('download', '拼多多机会商品-导出' + new Date().toLocaleString() + '.xlsx');
			aLink.click();
		})
		.catch(() => {
			loading.value = false;
		});
};

const tableCols = ref<VxeTable.Columns[]>([
	{ width: 'auto', sortable: true, field: 'goodsName', title: '商品名称', align: 'center' },
	{ width: 'auto', field: 'images', title: '商品图片', align: 'center', type: 'image' },
	{ width: 'auto', sortable: true, field: 'price', title: '价格', formatter: 'fmtAmt2', align: 'right' },
	{ width: 'auto', sortable: true, field: 'hotValue', title: '热度', align: 'right' },
]);
</script>

<style scoped lang="scss">
.numRangeCss {
	display: flex;
	align-items: center;
	gap: 10px;
	margin: 0 5px 5px 0;
	font-size: 12px;
	.publicCss {
		margin-bottom: 0px;
	}
}
</style>
