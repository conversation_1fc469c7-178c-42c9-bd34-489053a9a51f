<template>
	<Container v-loading="loading">
		<template #header>
			<div class="topCss">
				<el-input v-model.trim="query.goodsName" placeholder="商品名称" clearable maxlength="50" class="publicCss" />
				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
					<el-button type="primary" @click="exportProps">导出</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				id="supplyQuotationJd202507201327"
				:tableCols="tableCols"
				:query="query"
				:query-api="GetHotSaleGoodsJDGHBJList"
				showsummary
				isNeedDisposeProps
				@disposeProps="props.disposeProps"
			/>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, PropType } from 'vue';
import { GetHotSaleGoodsJDGHBJList, ExportHotSaleGoodsJDGHBJList } from '/@/api/operatemanage/hotSaleGoodsData';
const props = defineProps({
	disposeProps: {
		type: Function as PropType<(data: any, callback: Function) => void>,
		required: true,
	},
});
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const query = ref({
	goodsName: '', //商品名称
});
const table = ref();
const loading = ref(false);
const exportProps = async () => {
	loading.value = true;
	await ExportHotSaleGoodsJDGHBJList({ ...query.value, ...table.value.query })
		.then((data: any) => {
			loading.value = false;
			const aLink = document.createElement('a');
			let blob = new Blob([data], { type: 'application/vnd.ms-excel' });
			aLink.href = URL.createObjectURL(blob);
			aLink.setAttribute('download', '京东供货报价-导出' + new Date().toLocaleString() + '.xlsx');
			aLink.click();
		})
		.catch(() => {
			loading.value = false;
		});
};
const tableCols = ref<VxeTable.Columns[]>([
	{ width: 'auto', sortable: true, field: 'goodsName', title: '商品名称', align: 'center' },
	{ width: 'auto', field: 'images', title: '商品图片', align: 'center', type: 'image' },
	{ width: 'auto', sortable: true, field: 'saleCount', title: '累计销量', align: 'right', formatter: (row: any) => row.saleCountStr || '' },
]);
</script>

<style scoped lang="scss"></style>
