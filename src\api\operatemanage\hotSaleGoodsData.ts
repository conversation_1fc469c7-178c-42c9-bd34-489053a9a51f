import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_OperateManage}/HotSaleGoodsData/`;
//热销品数据
//拼多多机会商品-查询
export const GetHotSaleGoodsPddJHSPList = (params: any, config = {}) => {
	return request.post(apiPrefix + 'GetHotSaleGoodsPddJHSPList', params, config);
};

//拼多多机会商品-导出
export const ExportHotSaleGoodsPddJHSPList = (params: any, config = { responseType: 'blob' as any }) => {
	return request.post(apiPrefix + 'ExportHotSaleGoodsPddJHSPList', params, config);
};

//抖音机会商品-查询
export const GetHotSaleGoodsDYJHSPList = (params: any, config = {}) => {
	return request.post(apiPrefix + 'GetHotSaleGoodsDYJHSPList', params, config);
};

//抖音机会商品-导出
export const ExportHotSaleGoodsDYJHSPList = (params: any, config = { responseType: 'blob' as any }) => {
	return request.post(apiPrefix + 'ExportHotSaleGoodsDYJHSPList', params, config);
};

//京东供货报价-查询
export const GetHotSaleGoodsJDGHBJList = (params: any, config = {}) => {
	return request.post(apiPrefix + 'GetHotSaleGoodsJDGHBJList', params, config);
};

//京东供货报价-导出
export const ExportHotSaleGoodsJDGHBJList = (params: any, config = { responseType: 'blob' as any }) => {
	return request.post(apiPrefix + 'ExportHotSaleGoodsJDGHBJList', params, config);
};

//跨境好价跟卖-查询
export const GetHotSaleGoodsKJHJGMList = (params: any, config = {}) => {
	return request.post(apiPrefix + 'GetHotSaleGoodsKJHJGMList', params, config);
};

//跨境好价跟卖-导出
export const ExportHotSaleGoodsKJHJGMJList = (params: any, config = { responseType: 'blob' as any }) => {
	return request.post(apiPrefix + 'ExportHotSaleGoodsKJHJGMJList', params, config);
};
