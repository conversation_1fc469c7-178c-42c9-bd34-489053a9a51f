<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" class="demo-tabs w100 h100">
				<el-tab-pane label="拼多多机会商品" name="first" class="h100" lazy>
					<opportunityGoodPdd :shopList="shopList.filter((item: any) => item.platform === 2)" :disposeProps="createDisposeProps(['images'])" />
				</el-tab-pane>
				<el-tab-pane label="抖音机会商品" name="second" class="h100" lazy>
					<opportunityGoodDy :disposeProps="createDisposeProps(['images'])" />
				</el-tab-pane>
				<el-tab-pane label="京东供货报价" name="third" class="h100" lazy>
					<supplyQuotationJd :disposeProps="createDisposeProps(['images'])" />
				</el-tab-pane>
				<el-tab-pane label="跨境好价跟卖" name="forth" class="h100" lazy>
					<goodPriceFollowSellKj :disposeProps="createDisposeProps(['pic1', 'pic2'])" />
				</el-tab-pane>
			</el-tabs>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted } from 'vue';
import { GetShopList } from '/@/api/operatemanage/shop';
const activeName = ref('first');
const opportunityGoodPdd = defineAsyncComponent(() => import('./components/opportunityGoodPdd.vue'));
const opportunityGoodDy = defineAsyncComponent(() => import('./components/opportunityGoodDy.vue'));
const supplyQuotationJd = defineAsyncComponent(() => import('./components/supplyQuotationJd.vue'));
const goodPriceFollowSellKj = defineAsyncComponent(() => import('./components/goodPriceFollowSellKj.vue'));
const shopList = ref<any[]>([]);

// 通用的图片数据处理方法
const createDisposeProps = (imageFields: string[]) => {
	return (data: any, callback: Function) => {
		data.allData.list.forEach((item: any) => {
			imageFields.forEach((fieldName: string) => {
				if (item[fieldName] && typeof item[fieldName] === 'string') {
					// 检查是否是单个URL（不是JSON格式）
					if (item[fieldName].startsWith('http') && !item[fieldName].startsWith('[')) {
						// 单个URL，转换为数组格式
						item[fieldName] = [item[fieldName]];
					} else {
						// JSON字符串格式，尝试解析
						try {
							item[fieldName] = JSON.parse(item[fieldName]);
						} catch (error) {
							// 尝试正则提取所有 http/https 图片 URL
							const urlMatches = item[fieldName].match(/https?:\/\/[^\s'",\[\]]+\.(jpeg|jpg|png|webp|gif|bmp|svg)/gi);
							item[fieldName] = urlMatches && urlMatches.length > 0 ? urlMatches : [];
						}
					}
				}
			});
		});
		callback(data);
	};
};
onMounted(async () => {
	const { data, success } = await GetShopList({ platform: '', currentPage: 1, PageSize: 100000 });
	if (success) {
		shopList.value = data.list.map((item: any) => {
			return {
				value: item.shopCode,
				label: item.shopName,
				platform: item.platform,
			};
		});
	}
});
</script>

<style scoped lang="scss"></style>
