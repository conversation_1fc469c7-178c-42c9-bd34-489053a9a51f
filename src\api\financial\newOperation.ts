import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_Financial}/NewOperation/`;

//普通商品资料-查询
export const GetInitiateApprovalProcessList = (params: any, config = {}) => request.post(apiPrefix + 'GetInitiateApprovalProcessList', params, config);

//普通商品资料-导入
export const ImportAutomatedBrushingProcess = (params: any, config = {}) => request.post(apiPrefix + 'ImportAutomatedBrushingProcess', params, config);

//普通商品资料-发起流程
export const InitiateApprovalProcess = (params: any, config = {}) => request.post(apiPrefix + 'InitiateApprovalProcess', params, config);

//普通商品资料-导出
export const ExportInitiateApprovalProcess = (params: any, config = {}) => request.post(apiPrefix + 'ExportInitiateApprovalProcess', params, config);

//普通商品资料-编辑
export const EditInitiateApprovalProcess = (params: any, config = {}) => request.post(apiPrefix + 'EditInitiateApprovalProcess', params, config);
