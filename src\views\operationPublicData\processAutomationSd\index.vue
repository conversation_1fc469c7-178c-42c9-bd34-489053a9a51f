<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange
					class="publicCss"
					startPlaceholder="下单开始时间"
					endPlaceholder="下单结束时间"
					v-model:startDate="query.startDate"
					v-model:endDate="query.endDate"
					style="width: 200px"
					:clearable="false"
				/>
				<el-select v-model="query.status" class="publicCss" placeholder="状态" clearable>
					<el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.platform" class="publicCss" placeholder="平台" clearable filterable>
					<el-option v-for="item in platformlist.filter((x) => x.label !== '未知')" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-input v-model.trim="query.processNo" placeholder="流程号" clearable maxlength="50" class="publicCss" />
				<el-select v-model="query.groupId" placeholder="运营组" class="publicCss" clearable filterable>
					<el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<el-input v-model.trim="query.createdUserName" placeholder="导入人" clearable maxlength="50" class="publicCss" />
				<el-input v-model.trim="query.proCode" placeholder="商品id" clearable maxlength="50" class="publicCss" />
				<div class="pb5">
					<el-button type="primary" @click="getList">查询</el-button>
					<el-button type="primary" @click="startImport">导入</el-button>
					<el-button type="primary" @click="exportProps">导出</el-button>
					<el-button type="primary" @click="initiateMethod">发起刷单流程</el-button>
				</div>
			</div>
		</template>
		<template #content v-loading="loading">
			<vxetable
				ref="table"
				id="processAutomationSdIndex202508011001"
				:tableCols="tableCols"
				:query="query"
				:query-api="GetInitiateApprovalProcessList"
				showsummary
				isNeedCheckBox
				@select="onCheckBoxMethod"
			>
				<template #toolbar_buttons></template>
			</vxetable>

			<!-- ==================== 编辑对话框 ==================== -->
			<el-dialog v-model="editVisible" title="编辑" width="800" draggable overflow style="margin-top: -18vh !important" @close="handleClose" :close-on-click-modal="false">
				<div style="padding-top: 10px" v-loading="listLoading">
					<el-form :model="singleform" :rules="singlerules" ref="ruleFormRef" class="form-container" label-width="40px" height="500px">
						<el-row :gutter="20" class="form-row">
							<el-col :span="6">
								<el-form-item label="平台" label-width="100px" prop="platform">
									<el-select v-model="singleform.platform" style="width: 100%" placeholder="平台" clearable filterable>
										<el-option v-for="item in platformlist.filter((x) => x.label !== '未知')" :key="item.value" :label="item.label" :value="item.value" />
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="小组" label-width="100px" prop="groupId">
									<el-select v-model="singleform.groupId" placeholder="小组" style="width: 100%" clearable filterable>
										<el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="下单日期" label-width="100px" prop="orderDate">
									<el-date-picker v-model="singleform.orderDate" type="date" placeholder="下单日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :clearable="false" style="width: 100%" />
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="返款日期" label-width="100px" prop="refundDate">
									<el-date-picker v-model="singleform.refundDate" type="date" placeholder="下单日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :clearable="false" style="width: 100%" />
								</el-form-item>
							</el-col>
						</el-row>
					</el-form>
				</div>
				<template #footer>
					<div style="display: flex; justify-content: center; align-items: center; gap: 20px">
						<el-button @click="editVisible = false">取消</el-button>
						<el-button type="primary" @click="onSingleSave(ruleFormRef)" :disabled="listLoading">确定</el-button>
					</div>
				</template>
			</el-dialog>

			<!-- ==================== 发起营销费用对话框 ==================== -->
			<el-dialog v-model="initiateVisible" title="发起刷单流程" width="950" draggable overflow style="margin-top: -3vh !important" @close="initiateVisible = false" :close-on-click-modal="false">
				<div style="padding-top: 10px" v-loading="initiateLoading">
					<el-form :model="initiateform" :rules="initiateRules" ref="initiateRef" class="form-container" label-width="70px">
						<el-row :gutter="20" class="form-row">
							<el-col :span="6">
								<el-form-item label="平台" prop="platform">
									<el-select v-model="initiateform.platform" class="publicCss" placeholder="平台" clearable filterable>
										<el-option v-for="item in platformlist.filter((x) => x.label !== '未知')" :key="item.value" :label="item.label" :value="item.value" />
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="付款方式" prop="paymentMethod">
									<el-input v-model.trim="initiateform.paymentMethod" placeholder="付款方式" class="btnGroup" clearable maxlength="50" />
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="账号名" prop="accountName">
									<el-input v-model.trim="initiateform.accountName" placeholder="账号名" class="btnGroup" clearable maxlength="50" />
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="流程区域" prop="checkEare">
									<el-select v-model="initiateform.checkEare" class="publicCss" placeholder="请选择流程区域" clearable filterable>
										<el-option v-for="item in regionalOptions" :key="item.value" :label="item.label" :value="item.value" />
									</el-select>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="20" class="form-row">
							<el-col :span="8">
								<el-form-item label="账号" prop="account">
									<el-input v-model.trim="initiateform.account" placeholder="账号" class="btnGroup" clearable maxlength="50" />
								</el-form-item>
							</el-col>
							<el-col :span="8">
								<el-form-item label="开户行" prop="openBank">
									<el-input v-model.trim="initiateform.openBank" placeholder="开户行" class="btnGroup" clearable maxlength="50" />
								</el-form-item>
							</el-col>
							<el-col :span="8">
								<el-form-item label="支付日期" prop="payDate">
									<el-date-picker v-model="initiateform.payDate" type="date" placeholder="支付日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :clearable="false" style="width: 100%" />
								</el-form-item>
							</el-col>
						</el-row>
						<el-descriptions direction="vertical" :column="4" :size="'default'" border label-align="right">
							<el-descriptions-item label="申请事由">{{ initiateform.applyReason }}</el-descriptions-item>
							<el-descriptions-item label="报销/预支">{{ initiateform.reimbursementOrAdvancePayment }}</el-descriptions-item>
							<el-descriptions-item label="店铺编码">{{ initiateform.shopCode }}</el-descriptions-item>
							<el-descriptions-item label="店铺">{{ initiateform.shopName }}</el-descriptions-item>
							<el-descriptions-item label="费用分类">{{ initiateform.costClassification }}</el-descriptions-item>
							<el-descriptions-item label="金额（元）">{{ initiateform.amount }}</el-descriptions-item>
							<el-descriptions-item label="大写">{{ numberToChinese(initiateform.amount || 0) }}</el-descriptions-item>
							<!-- <el-descriptions-item label="付款方式">{{ initiateform.paymentMethod }}</el-descriptions-item>
							<el-descriptions-item label="账户名">{{ initiateform.accountName }}</el-descriptions-item>
							<el-descriptions-item label="账号">{{ initiateform.account }}</el-descriptions-item>
							<el-descriptions-item label="开户行">{{ initiateform.openBank }}</el-descriptions-item>
							<el-descriptions-item label="支付日期">{{ initiateform.payDate }}</el-descriptions-item> -->
							<el-descriptions-item label="附件链接">{{ initiateform.attachmentLink }}</el-descriptions-item>
						</el-descriptions>
						<!-- 分割线 -->
						<el-divider style="margin: 20px 0">
							<span style="color: #909399; font-size: 14px">明细列表</span>
						</el-divider>
						<vxetable
							:height="'350'"
							v-if="initiateVisible"
							:isNeedQueryApi="false"
							:isNeedPager="false"
							:remote="false"
							:isNeedTools="false"
							ref="table1"
							id="processAutomationSdIndex20250726163733"
							:tableCols="tableCols1"
							:data="checkBoxList"
							showsummary
						>
						</vxetable>
					</el-form>
				</div>
				<template #footer>
					<div style="display: flex; justify-content: center; align-items: center; gap: 20px">
						<el-button @click="initiateVisible = false">取消</el-button>
						<el-button type="primary" @click="initiateProps" :disabled="initiateLoading">确定</el-button>
					</div>
				</template>
			</el-dialog>

			<el-dialog title="导入数据" v-model="importVisible" width="30%" draggable overflow :close-on-click-modal="false" append-to-body style="margin-top: -30vh !important">
				<template #header>
					<div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px">
						<div>导入数据</div>
						<div><el-button type="primary" @click="downFile">下载导入模版</el-button></div>
					</div>
				</template>
				<div style="height: 100px">
					<el-upload
						ref="uploadFile"
						class="upload-demo"
						:auto-upload="false"
						:multiple="false"
						:limit="1"
						action=""
						accept=".xlsx"
						:file-list="fileLists"
						:data="fileparm"
						:http-request="onUploadFile"
						:on-success="onUploadSuccess"
						:on-change="onUploadChange"
						:on-remove="onUploadRemove"
					>
						<template #trigger>
							<el-button size="small" type="primary">选取文件</el-button>
						</template>
						<el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading" @click="onSubmitUpload" v-reclick>{{ uploadLoading ? '上传中' : '上传' }}</el-button>
					</el-upload>
				</div>
				<div style="display: flex; justify-content: end; align-items: center">
					<el-button @click="importVisible = false">关闭</el-button>
				</div>
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted, nextTick } from 'vue';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const uploadMf = defineAsyncComponent(() => import('/@/components/yhCom/uploadMf.vue'));
import { getDirectorGroupList } from '/@/api/operatemanage/shop';
import { GetInitiateApprovalProcessList, ImportAutomatedBrushingProcess, InitiateApprovalProcess, ExportInitiateApprovalProcess, EditInitiateApprovalProcess } from '/@/api/financial/newOperation';
import { platformlist, downLoadFile, numberToChinese } from '/@/utils/tools';
import { ElMessage, FormInstance, ElMessageBox } from 'element-plus';
const refuploadMf = ref<InstanceType<typeof uploadMf> | null>(null);
const refuploadMfA = ref<InstanceType<typeof uploadMf> | null>(null);
const ruleFormRef = ref();
const initiateRef = ref();
import { debounce } from 'lodash-es';
const time = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
const checkBoxList = ref<any[]>([]);
const query = ref({
	startDate: time, //开始时间
	endDate: time, //结束时间
	status: null, //状态
	platform: null, //平台
	processNo: '', //流程号
	groupId: null, //运营组
	createdUserName: '', //导入人
	proCode: '', //商品id
});
const editVisible = ref(false);
const listLoading = ref(false);
const regionalOptions = ref<Public.options[]>([
	{ label: '南昌', value: 1 },
	{ label: '义乌', value: 2 },
	{ label: '武汉', value: 3 },
]);
const singleform = ref<{
	id: string | number;
	payPictureArr: string[];
	shopPictureArr: string[];
}>({
	id: '',
	payPictureArr: [],
	shopPictureArr: [],
});
const singlerules = ref({
	payPictureArr: [{ required: true, message: '请输入支付截图', trigger: 'blur' }],
	shopPictureArr: [{ required: true, message: '请输入店铺截图', trigger: 'blur' }],
});
const statusList = ref<Public.options[]>([
	{ label: '异常', value: 0 },
	{ label: '待发起', value: 1 },
	{ label: '已发起', value: 2 },
	{ label: '已撤销', value: 3 },
	{ label: '审批不通过', value: 4 },
	{ label: '已通过', value: 5 },
]);
const table = ref();
const loading = ref(false);
const groupList = ref<any[]>([]);

const importVisible = ref(false);
const uploadLoading = ref(false);
const fileLists = ref([]);
const uploadFile = ref();
const fileparm = ref({});

// 银行选项常量
const bankOptions = ref(['招商银行', '中国工商银行', '农商银行', '中信银行', '中国农业银行', '中国建设银行', '交通银行', '浙商银行', '其他（备注）', '中国银行', '中国邮政储蓄银行']);

const initiateVisible = ref(false);
const initiateLoading = ref(false);
const initiateform = ref<any>({
	platform: null,
	accountName: '',
	account: '',
	openBank: '',
	payDate: null as string | null,
	checkIds: [] as (string | number)[],
	paymentMethod: '',
	checkEare: null,
});

const initiateRules = ref({
	platform: [{ required: true, message: '请选择平台', trigger: 'change' }],
	accountName: [{ required: true, message: '请输入账号名', trigger: 'blur' }],
	account: [{ required: true, message: '请输入账号', trigger: 'blur' }],
	openBank: [{ required: true, message: '请输入开户行', trigger: 'blur' }],
	payDate: [{ required: true, message: '请选择支付日期', trigger: 'change' }],
	paymentMethod: [{ required: true, message: '请输入付款方式', trigger: 'blur' }],
	checkEare: [{ required: true, message: '请选择流程区域', trigger: 'change' }],
});

const initiateMethod = async () => {
	if (checkBoxList.value.length === 0) {
		ElMessage.error('请选择要发起的数据');
		return;
	}
	if (checkBoxList.value.some((item: any) => item.status === 2 || item.status === 0 || item.status === 5)) {
		ElMessage.error('勾选的项中包含已发起、已通过、异常的数据，不能发起');
		return;
	}
	initiateVisible.value = true;
	await nextTick();
	initiateRef.value?.resetFields();
	let a = checkBoxList.value[0] ? checkBoxList.value[0] : {};
	initiateform.value = a ? JSON.parse(JSON.stringify(a)) : {};
	initiateform.value.checkIds = checkBoxList.value.map((item: any) => item.id);
	initiateform.value.payDate = a.payDate ? dayjs(a.payDate).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD');
	initiateform.value.amount = a.amounted ? a.amounted : 0;
	initiateform.value.applyReason = '私域费用';
	initiateform.value.reimbursementOrAdvancePayment = '报销';
	initiateform.value.costClassification = '私域';
};

const initiateProps = async () => {
	if (!initiateRef.value) return;
	await initiateRef.value.validate(async (valid: any, fields: any) => {
		if (valid) {
			ElMessageBox.confirm('确定要发起刷单流程吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(async () => {
					initiateLoading.value = true;
					const { success } = await InitiateApprovalProcess(initiateform.value);
					if (success) {
						ElMessage.success('发起成功');
						getList();
					}
					initiateLoading.value = false;
				})
				.catch(() => {
					initiateLoading.value = false;
				});
		}
	});
};

const onUploadFile = async (item: any) => {
	if (!item || !item.file || !item.file.size) {
		window.$message({ message: '请先上传文件', type: 'warning' });
		return false;
	}
	uploadLoading.value = true;
	const form = new FormData();
	form.append('upfile', item.file);
	try {
		const { success, msg, data } = await ImportAutomatedBrushingProcess(form);
		if (!data.success) {
			window.$message({ message: data.msg || '导入失败', type: 'error' });
			return;
		}
		if (data.success) window.$message({ message: '上传成功,正在导入中...', type: 'success' });
		importVisible.value = false;
		table.value.refreshTable(true);
	} catch (error) {
		console.log('上传失败', error);
	} finally {
		uploadLoading.value = false;
	}
};

const downFile = () => {
	downLoadFile('https://nanc.yunhanmy.com:10010/media/video/20250801/1951150330935222272.xlsx', '刷单流程自动化导入模版.xlsx');
};

const onUploadSuccess = (response: any, file: any, fileList: any) => {
	fileLists.value.splice(fileList.indexOf(file), 1);
	fileLists.value = [];
	importVisible.value = false;
};

const onUploadChange = async (file: any, fileList: any) => {
	fileLists.value.splice(0, fileList.length - 1);
	fileLists.value = fileList;
};

const onUploadRemove = (file: any, fileList: any) => {
	fileLists.value.splice(fileList.indexOf(file), 1);
};

const onSubmitUpload = () => {
	if (fileLists.value.length == 0) {
		window.$message({ message: '请先上传文件', type: 'warning' });
		return false;
	}
	uploadFile.value.submit();
};

//导入弹窗
const startImport = () => {
	fileLists.value = [];
	importVisible.value = true;
};

const exportProps = async () => {
	loading.value = true;
	await ExportInitiateApprovalProcess({ ...query.value, ...table.value.query })
		.then((data: any) => {
			loading.value = false;
			window.$message[data.success ? 'success' : 'error'](data.msg || (data.success ? '导出成功,稍后请到下载管理查看' : '导出失败'));
		})
		.catch(() => {
			loading.value = false;
		});
};

const onSingleSave = debounce(async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate(async (valid, fields) => {
		if (valid) {
			listLoading.value = true;
			const params = {
				...singleform.value,
				payPicture: singleform.value.payPictureArr.join(',') || '',
				shopPicture: singleform.value.shopPictureArr.join(',') || '',
				shopPictureArr: undefined,
				payPictureArr: undefined,
				marketCostId: singleform.value.id,
			};
			const { success } = await EditInitiateApprovalProcess(params);
			if (success) {
				ElMessage.success('编辑成功');
				editVisible.value = false;
				getList();
			}
			listLoading.value = false;
		}
	});
}, 500);

const handleClose = () => {
	editVisible.value = false;
	ruleFormRef.value.resetFields();
};

const handleEdit = (row: any) => {
	editVisible.value = true;
	singleform.value = { ...row };
	singleform.value.payPictureArr = row.payPicture.split(',');
	singleform.value.shopPictureArr = row.shopPicture.split(',');
};

const onCheckBoxMethod = (val: any) => {
	checkBoxList.value = val;
};

const getList = () => {
	table.value.query.currentPage = 1;
	table.value.refreshTable(true);
	checkBoxList.value = [];
	table.value.clearSelection();
};

const tableCols1 = ref<VxeTable.Columns[]>([
	{ width: '100', sortable: true, field: 'platform', title: '平台', align: 'center', formatter: 'formatPlatform' },
	{ width: '100', sortable: true, field: 'groupName', title: '小组', align: 'center' },
	{ width: '100', sortable: true, field: 'orderDate', title: '下单日期', align: 'center', formatter: 'formatDate' },
	{ width: '100', sortable: true, field: 'refundDate', title: '返款日期', align: 'center', formatter: 'formatDate' },
	{ width: '100', sortable: true, field: 'orderNo', title: '订单编号', align: 'center' },
	{ width: '100', sortable: true, field: 'amounted', title: '金额', align: 'right', formatter: 'fmtAmt2' },
	{ width: '100', sortable: true, field: 'commission', title: '佣金', align: 'right', formatter: 'fmtAmt2' },
	{ width: '100', sortable: true, field: 'shopCode', title: '店铺编码', align: 'center' },
	{ width: '100', sortable: true, field: 'shopName', title: '店铺', align: 'center' },
	{ width: '100', sortable: true, field: 'proName', title: '商品', align: 'center' },
	{ width: '100', sortable: true, field: 'sendOutGood', title: '发货', align: 'center' },
	{ width: '100', sortable: true, field: 'proCode', title: '商品ID', align: 'center' },
	{ width: '100', sortable: true, field: 'notes', title: '备注', align: 'center' },
	{ width: '100', sortable: true, field: 'brand', title: '品牌', align: 'center' },
]);

const tableCols = ref<VxeTable.Columns[]>([
	{ width: '80', sortable: true, field: 'status', title: '状态', align: 'center', formatter: (row: any) => statusList.value.find((item) => item.value === row.status)?.label },
	{ width: '100', sortable: true, field: 'platform', title: '平台', align: 'center', formatter: 'formatPlatform' },
	{ width: '100', sortable: true, field: 'processNo', title: '流程号', align: 'center' },
	{ width: '100', sortable: true, field: 'groupName', title: '小组', align: 'center' },
	{ width: '100', sortable: true, field: 'orderDate', title: '下单日期', align: 'center', formatter: 'formatDate' },
	{ width: '100', sortable: true, field: 'refundDate', title: '返款日期', align: 'center', formatter: 'formatDate' },
	{ width: '100', sortable: true, field: 'orderNo', title: '订单编号', align: 'center' },
	{ width: '100', sortable: true, field: 'amounted', title: '金额', align: 'right', formatter: 'fmtAmt2' },
	{ width: '100', sortable: true, field: 'commission', title: '佣金', align: 'right', formatter: 'fmtAmt2' },
	{ width: '100', sortable: true, field: 'shopCode', title: '店铺编码', align: 'center' },
	{ width: '100', sortable: true, field: 'shopName', title: '店铺', align: 'center' },
	{ width: '100', sortable: true, field: 'proName', title: '商品', align: 'center' },
	{ width: '100', sortable: true, field: 'sendOutGood', title: '发货', align: 'center' },
	{ width: '100', sortable: true, field: 'proCode', title: '商品ID', align: 'center' },
	{ width: '100', sortable: true, field: 'notes', title: '备注', align: 'center' },
	{ width: '100', sortable: true, field: 'brand', title: '品牌', align: 'center' },
	{
		title: '操作',
		align: 'center',
		width: '80',
		type: 'btnList',
		minWidth: '80',
		field: 'operation',
		btnList: [
			{
				title: '编辑',
				handle: handleEdit,
				isDisabled(row) {
					return row.status == 2 ? true : false;
				},
			},
		],
		fixed: 'right',
	},
]);
onMounted(async () => {
	let { data, success } = await getDirectorGroupList();
	if (data && success) {
		groupList.value = data.map((item: any) => {
			return { value: item.key, label: item.value };
		});
	}
});
</script>

<style scoped lang="scss">
.form-container {
	.form-row {
		margin-bottom: 20px;

		&:last-child {
			margin-bottom: 0;
		}
	}
}
</style>
