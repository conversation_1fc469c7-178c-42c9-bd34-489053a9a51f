<template>
	<Container v-loading="loading">
		<template #header>
			<div class="topCss">
				<el-input v-model.trim="query.clueSKC" placeholder="SKC" clearable maxlength="50" class="publicCss" />
				<el-input v-model.trim="query.goodsName" placeholder="商品名称" clearable maxlength="50" class="publicCss" />
				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
					<el-button type="primary" @click="exportProps">导出</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				id="goodPriceFollowSellKj202507201339"
				:tableCols="tableCols"
				:query="query"
				:query-api="GetHotSaleGoodsKJHJGMList"
				showsummary
				isNeedDisposeProps
				@disposeProps="props.disposeProps"
			/>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, PropType } from 'vue';
import { GetHotSaleGoodsKJHJGMList, ExportHotSaleGoodsKJHJGMJList } from '/@/api/operatemanage/hotSaleGoodsData';
const props = defineProps({
	disposeProps: {
		type: Function as PropType<(data: any, callback: Function) => void>,
		required: true,
	},
});
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const query = ref({
	clueSKC: '', //SKC
	goodsName: '', //商品名称
});
const table = ref();
const loading = ref(false);
const exportProps = async () => {
	loading.value = true;
	await ExportHotSaleGoodsKJHJGMJList({ ...query.value, ...table.value.query })
		.then((data: any) => {
			loading.value = false;
			const aLink = document.createElement('a');
			let blob = new Blob([data], { type: 'application/vnd.ms-excel' });
			aLink.href = URL.createObjectURL(blob);
			aLink.setAttribute('download', '跨境好价跟卖-导出' + new Date().toLocaleString() + '.xlsx');
			aLink.click();
		})
		.catch(() => {
			loading.value = false;
		});
};
const tableCols = ref<VxeTable.Columns[]>([
	{ width: '160', sortable: true, field: 'clueSKC', title: 'SKC', align: 'center' },
	{ width: 'auto', sortable: true, field: 'goodsName', title: '商品名称', align: 'center' },
	{ width: '100', field: 'pic1', title: '图片1', align: 'center', type: 'image' },
	{ width: '100', field: 'pic2', title: '图片2', align: 'center', type: 'image' },
	{ width: '250', sortable: true, field: 'categoryName', title: '选品需求分类', align: 'center' },
	{ width: '100', sortable: true, field: 'goodsColour', title: '颜色', align: 'center' },
	{ width: '250', sortable: true, field: 'specification', title: '必报规格', align: 'center' },
	{ width: '100', sortable: true, field: 'price', title: '提报价格', formatter: 'fmtAmt2', align: 'right' },
	{ width: '100', sortable: true, field: 'monthlySalesVolume', title: '月销售数量', align: 'right' },
	{ width: '100', sortable: true, field: 'salesRevenue', title: '销售额', formatter: 'fmtAmt2', align: 'right' },
]);
</script>

<style scoped lang="scss"></style>
