import request from '/@/utils/yhrequest';
const apiPrefix = `${import.meta.env.VITE_APP_BASE_API_OperateManage}/ProductManager/`;

//违规链接下架-新增
export const ViolationDataStatisticsStyleCodeAdd = (params: any, config = {}) => {
	return request.post(apiPrefix + 'ViolationDataStatisticsStyleCodeAdd', params, config);
};

//违规链接下架-查询
export const ViolationDataStatisticsStyleCodeQuery = (params: any, config = {}) => {
	return request.post(apiPrefix + 'ViolationDataStatisticsStyleCodeQuery', params, config);
};

//违规链接下架-删除
export const ViolationDataStatisticsStyleCodeDelete = (params: any, config = {}) => {
	return request.post(apiPrefix + 'ViolationDataStatisticsStyleCodeDelete', params, config);
};

//违规链接下架-系列编码查询产品管理
export const ViolationDataStatisticsQueryProduct = (params: any, config = {}) => {
	return request.post(apiPrefix + 'ViolationDataStatisticsQueryProduct', params, config);
};

//获取商品上下架列表
export const GetProductUpDownList = (params: any, config = {}) => {
	return request.post(apiPrefix + 'GetProductUpDownList', params, config);
};

//批量上下架
export const BatchProductUpDown = (params: any, config = {}) => {
	return request.post(apiPrefix + 'BatchProductUpDown', params, config);
};

//导出商品上下架列表
export const ExportProductUpDownList = (params: any, config = { responseType: 'blob' as any }) => {
	return request.post(apiPrefix + 'ExportProductUpDownList', params, config);
};

//删除PDD商品链接
export const DeletePDDProduct = (params: any, config = {}) => {
	return request.post(apiPrefix + 'DeletePDDProduct', params, config);
};

//获取商品上下架日志列表
export const GetProductUpDownLogPageList = (params: any, config = {}) => {
	return request.post(apiPrefix + 'GetProductUpDownLogPageList', params, config);
};

//导出商品上下架日志列表
export const ExportProductUpDownLogList = (params: any, config = { responseType: 'blob' as any }) => {
	return request.post(apiPrefix + 'ExportProductUpDownLogList', params, config);
};

// 导出批量上下架日志
export const GetProductUpDownLogExport = (params: any, config = {}) => {
	return request.post(apiPrefix + 'GetProductUpDownLogExport', params, config);
};

// 违规词管理-查询
export const SensitiveWordsQuery = (params: any, config = {}) => {
	return request.post(apiPrefix + 'SensitiveWordsQuery', params, config);
};

// 违规词管理-新增
export const SensitiveWordsAdd = (params: any, config = {}) => {
	return request.post(apiPrefix + 'SensitiveWordsAdd', params, config);
};

// 违规词管理-删除
export const SensitiveWordsDelete = (params: any, config = {}) => {
	return request.post(apiPrefix + 'SensitiveWordsDelete', params, config);
};

// 检查产品标题是否含有违规词
export const ProdContainsSensitiveWords = (params: any, config = {}) => {
	return request.post(apiPrefix + 'ProdContainsSensitiveWords', params, config);
};

// 导出检查产品标题是否含有违规词
export const ExportProdContainsSensitiveWords = (params: any, config = {}) => {
	return request.post(apiPrefix + 'ExportProdContainsSensitiveWords', params, config);
};

// 违规链接下架-系列编码查询产品管理-列表导出
export const ExportViolationDataStatisticsQueryProduct = (params: any, config = { responseType: 'blob' as any }) => {
	return request.post(apiPrefix + 'ExportViolationDataStatisticsQueryProduct', params, config);
};

// 剪辑人员业绩统计-剪辑人员配置-新增
export const EditingPersonnelConfigurationAdd = (params: any, config = {}) => {
	return request.post(apiPrefix + 'EditingPersonnelConfigurationAdd', params, config);
};

// 剪辑人员业绩统计-剪辑人员配置-查询
export const EditingPersonnelConfigurationQuery = (params: any, config = {}) => {
	return request.post(apiPrefix + 'EditingPersonnelConfigurationQuery', params, config);
};

// 剪辑人员业绩统计-剪辑人员配置-子级编辑
export const EditingPersonnelConfigurationEdit = (params: any, config = {}) => {
	return request.post(apiPrefix + 'EditingPersonnelConfigurationEdit', params, config);
};

// 剪辑人员业绩统计-剪辑人员配置-父子级删除
export const EditingPersonnelConfigurationDelete = (params: any, config = {}) => {
	return request.post(apiPrefix + 'EditingPersonnelConfigurationDelete', params, config);
};

// 剪辑人员业绩统计-剪辑人员配置-导出
export const ExportEditingPersonnelConfiguration = (params: any, config = { responseType: 'blob' as any }) => {
	return request.post(apiPrefix + 'ExportEditingPersonnelConfiguration', params, config);
};

// 剪辑人员业绩统计-剪辑人员配置-导入
export const ImportEditingPersonnelConfiguration = (params: any, config = {}) => {
	return request.post(apiPrefix + 'ImportEditingPersonnelConfiguration', params, config);
};

// 剪辑人员业绩统计-数据统计-查询
export const DataStatisticsQuery = (params: any, config = {}) => {
	return request.post(apiPrefix + 'DataStatisticsQuery', params, config);
};

// 剪辑人员业绩统计-数据统计-导出
export const ExportDataStatisticsDY = (params: any, config = { responseType: 'blob' as any }) => {
	return request.post(apiPrefix + 'ExportDataStatisticsDY', params, config);
};

// 违规词管理-白名单-新增
export const SensitiveWordsWhiteListAdd = (params: any, config = {}) => {
	return request.post(apiPrefix + 'SensitiveWordsWhiteListAdd', params, config);
};

// 违规词管理-白名单-查询
export const SensitiveWordsWhiteListQuery = (params: any, config = {}) => {
	return request.post(apiPrefix + 'SensitiveWordsWhiteListQuery', params, config);
};

// 违规词管理-白名单-删除
export const SensitiveWordsWhiteListDelete = (params: any, config = {}) => {
	return request.post(apiPrefix + 'SensitiveWordsWhiteListDelete', params, config);
};

// 违规词管理-白名单-模糊获取系列编码
export const WhiteListQueryStyleCode = (params: any, config = {}) => {
	return request.post(apiPrefix + 'WhiteListQueryStyleCode', params, config);
};

// 违规词管理-配置-导出
export const ExportSensitiveWords = (params: any, config = { responseType: 'blob' as any }) => {
	return request.post(apiPrefix + 'ExportSensitiveWords', params, config);
};
